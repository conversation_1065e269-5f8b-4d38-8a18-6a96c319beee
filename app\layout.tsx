import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "The Sixteen - Luxury Real Estate",
  description: "Discover exceptional luxury properties at prime locations. The Sixteen offers exclusive real estate opportunities with unparalleled elegance and sophistication.",
  keywords: ["luxury real estate", "premium properties", "exclusive homes", "The Sixteen", "luxury living"],
  authors: [{ name: "The Sixteen Real Estate" }],
  creator: "The Sixteen",
  publisher: "The Sixteen Real Estate",
  openGraph: {
    title: "The Sixteen - Luxury Real Estate",
    description: "Discover exceptional luxury properties at prime locations.",
    type: "website",
    locale: "en_US",
    siteName: "The Sixteen",
  },
  twitter: {
    card: "summary_large_image",
    title: "The Sixteen - Luxury Real Estate",
    description: "Discover exceptional luxury properties at prime locations.",
  },
  robots: "index, follow",
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Header />
        {children}
        <Footer />
      </body>
    </html>
  );
}
