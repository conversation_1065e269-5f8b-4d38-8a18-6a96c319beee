import { gsap } from 'gsap';

// GSAP Animation Utilities for Luxury Real Estate Website

export const luxuryAnimations = {
  // Fade in with luxury easing
  fadeInUp: (element: string | Element, delay: number = 0) => {
    return gsap.fromTo(
      element,
      {
        opacity: 0,
        y: 60,
        scale: 0.95,
      },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1.2,
        delay,
        ease: "power3.out",
      }
    );
  },

  // Stagger animation for property cards
  staggerFadeIn: (elements: string | Element[] | HTMLCollection, stagger: number = 0.2) => {
    return gsap.fromTo(
      elements,
      {
        opacity: 0,
        y: 40,
        scale: 0.9,
      },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        stagger,
        ease: "power2.out",
      }
    );
  },

  // Luxury hover animation
  luxuryHover: (element: string | Element) => {
    const tl = gsap.timeline({ paused: true });
    
    tl.to(element, {
      y: -8,
      scale: 1.02,
      boxShadow: "0 25px 50px -12px rgba(212, 175, 55, 0.25)",
      duration: 0.3,
      ease: "power2.out",
    });

    return {
      play: () => tl.play(),
      reverse: () => tl.reverse(),
    };
  },

  // Text reveal animation
  textReveal: (element: string | Element, delay: number = 0) => {
    return gsap.fromTo(
      element,
      {
        opacity: 0,
        y: 30,
        skewY: 3,
      },
      {
        opacity: 1,
        y: 0,
        skewY: 0,
        duration: 1,
        delay,
        ease: "power3.out",
      }
    );
  },

  // Parallax scroll effect
  parallaxScroll: (element: string | Element, speed: number = 0.5) => {
    return gsap.to(element, {
      yPercent: -50 * speed,
      ease: "none",
      scrollTrigger: {
        trigger: element,
        start: "top bottom",
        end: "bottom top",
        scrub: true,
      },
    });
  },

  // Count up animation for numbers
  countUp: (element: string | Element, endValue: number, duration: number = 2) => {
    const obj = { value: 0 };
    
    return gsap.to(obj, {
      value: endValue,
      duration,
      ease: "power2.out",
      onUpdate: () => {
        if (typeof element === 'string') {
          const el = document.querySelector(element);
          if (el) el.textContent = Math.round(obj.value).toLocaleString();
        } else {
          element.textContent = Math.round(obj.value).toLocaleString();
        }
      },
    });
  },

  // Luxury page transition
  pageTransition: () => {
    const tl = gsap.timeline();
    
    tl.to(".page-transition", {
      scaleY: 1,
      transformOrigin: "bottom",
      duration: 0.6,
      ease: "power4.inOut",
    })
    .to(".page-transition", {
      scaleY: 0,
      transformOrigin: "top",
      duration: 0.6,
      ease: "power4.inOut",
    }, "+=0.2");

    return tl;
  },
};

// Initialize GSAP defaults for luxury animations
export const initGSAP = () => {
  gsap.defaults({
    duration: 0.8,
    ease: "power2.out",
  });

  // Register ScrollTrigger if available
  if (typeof window !== 'undefined') {
    import('gsap/ScrollTrigger').then(({ ScrollTrigger }) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      gsap.registerPlugin(ScrollTrigger as any);
    }).catch(() => {
      // ScrollTrigger not available, skip registration
    });
  }
};