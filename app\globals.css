@import "tailwindcss";

/* Luxury Design System for The Sixteen */
:root {
  /* Primary Luxury Colors */
  --luxury-gold: #D4AF37;
  --luxury-champagne: #F7E7CE;
  --luxury-charcoal: #2C2C2C;
  --luxury-platinum: #E5E4E2;
  --luxury-cream: #F8F6F0;
  --luxury-pearl: #FAF0E6;
  
  /* Extended Palette */
  --luxury-gold-dark: #B8941F;
  --luxury-gold-light: #E6C85F;
  --luxury-charcoal-light: #3D3D3D;
  --luxury-charcoal-dark: #1A1A1A;
  --luxury-accent-bronze: #CD7F32;
  --luxury-accent-rose-gold: #E8B4B8;
  
  /* Semantic Colors */
  --background: #FFFFFF;
  --foreground: #2C2C2C;
  --muted: #F8F6F0;
  --muted-foreground: #6B7280;
  --border: #E5E4E2;
  --input: #FFFFFF;
  --ring: #D4AF37;
  
  /* Text Colors */
  --text-primary: #2C2C2C;
  --text-secondary: #6B7280;
  --text-muted: #9CA3AF;
  --text-luxury: #D4AF37;
  
  /* Component Colors */
  --card: #FFFFFF;
  --card-foreground: #2C2C2C;
  --popover: #FFFFFF;
  --popover-foreground: #2C2C2C;
  --primary: #D4AF37;
  --primary-foreground: #FFFFFF;
  --secondary: #F8F6F0;
  --secondary-foreground: #2C2C2C;
  --accent: #F7E7CE;
  --accent-foreground: #2C2C2C;
  --destructive: #EF4444;
  --destructive-foreground: #FFFFFF;
  
  /* Typography Scale */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  
  /* Spacing Scale */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(44, 44, 44, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(44, 44, 44, 0.1), 0 2px 4px -1px rgba(44, 44, 44, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(44, 44, 44, 0.1), 0 4px 6px -2px rgba(44, 44, 44, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(44, 44, 44, 0.1), 0 10px 10px -5px rgba(44, 44, 44, 0.04);
  --shadow-luxury: 0 25px 50px -12px rgba(212, 175, 55, 0.25);
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-luxury-gold: var(--luxury-gold);
  --color-luxury-champagne: var(--luxury-champagne);
  --color-luxury-charcoal: var(--luxury-charcoal);
  --color-luxury-platinum: var(--luxury-platinum);
  --color-luxury-cream: var(--luxury-cream);
  --color-luxury-pearl: var(--luxury-pearl);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-ring: var(--ring);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Base Styles */
* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Luxury Typography */
.luxury-heading {
  font-weight: 700;
  letter-spacing: -0.025em;
  color: var(--luxury-charcoal);
  line-height: 1.2;
}

.luxury-subheading {
  font-weight: 500;
  letter-spacing: -0.01em;
  color: var(--luxury-charcoal);
  line-height: 1.4;
}

.luxury-body {
  font-weight: 400;
  letter-spacing: 0.01em;
  color: var(--text-primary);
  line-height: 1.6;
}

.luxury-caption {
  font-weight: 500;
  letter-spacing: 0.05em;
  color: var(--text-secondary);
  text-transform: uppercase;
  font-size: var(--font-size-sm);
}

/* Luxury Gradients */
.luxury-gradient-gold {
  background: linear-gradient(135deg, var(--luxury-gold) 0%, var(--luxury-gold-light) 100%);
}

.luxury-gradient-champagne {
  background: linear-gradient(135deg, var(--luxury-champagne) 0%, var(--luxury-pearl) 100%);
}

.luxury-gradient-text {
  background: linear-gradient(135deg, var(--luxury-gold) 0%, var(--luxury-accent-bronze) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Luxury Effects */
.luxury-glass {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.luxury-shine {
  position: relative;
  overflow: hidden;
}

.luxury-shine::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.3), transparent);
  transition: left var(--transition-slow);
}

.luxury-shine:hover::before {
  left: 100%;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--luxury-cream);
}

::-webkit-scrollbar-thumb {
  background: var(--luxury-gold);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--luxury-gold-dark);
}

/* Selection Styling */
::selection {
  background: var(--luxury-champagne);
  color: var(--luxury-charcoal);
}

/* Focus Styles */
:focus-visible {
  outline: 2px solid var(--luxury-gold);
  outline-offset: 2px;
}

/* Hero Section Specific Styles */
.hero-video-overlay {
  background: linear-gradient(
    180deg,
    rgba(44, 44, 44, 0.3) 0%,
    rgba(44, 44, 44, 0.5) 50%,
    rgba(44, 44, 44, 0.8) 100%
  );
}

/* Animated scroll indicator */
@keyframes scroll-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

.scroll-indicator {
  animation: scroll-bounce 2s infinite;
}

/* Video background optimizations */
.hero-video {
  object-fit: cover;
  object-position: center;
}

/* Luxury glass morphism enhancement */
.luxury-glass-enhanced {
  backdrop-filter: blur(20px) saturate(180%);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  box-shadow: 0 8px 32px 0 rgba(44, 44, 44, 0.1);
}

/* Premium glass morphism for hero elements */
.luxury-glass-premium {
  backdrop-filter: blur(25px) saturate(200%);
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(212, 175, 55, 0.4);
  box-shadow: 
    0 8px 32px 0 rgba(44, 44, 44, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Enhanced luxury shine effect for hero buttons */
.luxury-shine-enhanced {
  position: relative;
  overflow: hidden;
}

.luxury-shine-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.luxury-shine-enhanced:hover::before {
  left: 100%;
}

/* Hero text glow effect */
.hero-text-glow {
  text-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
}

/* Statistics card hover effect */
.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(212, 175, 55, 0.2);
}

/* Property Showcase Specific Styles */
.property-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.property-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(212, 175, 55, 0.25);
}

/* Luxury gradient backgrounds */
.luxury-gradient-charcoal {
  background: linear-gradient(135deg, var(--luxury-charcoal) 0%, var(--luxury-charcoal-light) 100%);
}

.luxury-gradient-pearl {
  background: linear-gradient(135deg, var(--luxury-pearl) 0%, var(--luxury-cream) 100%);
}

/* Enhanced glass morphism for property cards */
.property-glass {
  backdrop-filter: blur(15px) saturate(180%);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(212, 175, 55, 0.15);
  box-shadow: 0 8px 32px 0 rgba(44, 44, 44, 0.08);
}

/* Property image overlays */
.property-image-overlay {
  background: linear-gradient(
    180deg,
    rgba(44, 44, 44, 0) 0%,
    rgba(44, 44, 44, 0.1) 30%,
    rgba(44, 44, 44, 0.6) 100%
  );
}

/* Property badge styling */
.property-badge {
  backdrop-filter: blur(10px);
  background: rgba(212, 175, 55, 0.9);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Luxury dialog styling */
.luxury-dialog {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(212, 175, 55, 0.2);
  box-shadow: 0 25px 50px -12px rgba(44, 44, 44, 0.25);
}

/* Property filter button animations */
.filter-button {
  position: relative;
  overflow: hidden;
}

.filter-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.filter-button:hover::before {
  left: 100%;
}

/* Enhanced scroll animations */
.scroll-fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.scroll-fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Property card image carousel */
.property-carousel {
  position: relative;
  overflow: hidden;
}

.property-carousel img {
  transition: transform 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.property-carousel:hover img {
  transform: scale(1.05);
}

/* Luxury text effects */
.luxury-text-shadow {
  text-shadow: 0 2px 4px rgba(44, 44, 44, 0.1);
}

.luxury-text-glow {
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
}

/* Property status indicators */
.status-available {
  background: linear-gradient(135deg, #10B981, #059669);
}

.status-pending {
  background: linear-gradient(135deg, #F59E0B, #D97706);
}

.status-sold {
  background: linear-gradient(135deg, #EF4444, #DC2626);
}

/* Enhanced mobile responsiveness */
@media (max-width: 768px) {
  .property-card {
    margin-bottom: 1rem;
  }
  
  .luxury-heading {
    font-size: 2rem;
    line-height: 1.1;
  }
  
  .luxury-subheading {
    font-size: 1.25rem;
    line-height: 1.3;
  }
  
  /* Mobile-optimized hero text */
  .hero-text-glow {
    font-size: 2.5rem !important;
    line-height: 0.9;
  }
  
  /* Improved mobile touch targets */
  .stats-card {
    min-height: 120px;
    padding: 1rem;
  }
  
  /* Mobile form improvements */
  .luxury-glass {
    padding: 1rem;
  }
  
  /* Reduce margins for better space utilization */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .luxury-heading {
    font-size: 2.5rem;
  }
  
  .property-card {
    margin-bottom: 1.5rem;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .property-card:hover {
    transform: none;
  }
  
  .luxury-shine:hover::before {
    left: 100%;
    transition: left 0.3s;
  }
  
  /* Increase button sizes for touch */
  .btn-touch {
    min-height: 44px;
    min-width: 44px;
  }
}

/* High contrast mode accessibility */
@media (prefers-contrast: high) {
  .luxury-glass {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid var(--luxury-gold);
  }
  
  .luxury-gradient-text {
    color: var(--luxury-gold);
    background: none;
    -webkit-text-fill-color: unset;
  }
}

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
  .property-card,
  .luxury-shine,
  .filter-button {
    transition: none;
  }
  
  .property-carousel img {
    transition: none;
  }
}

/* Print styles */
@media print {
  .property-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .luxury-glass {
    background: white;
    backdrop-filter: none;
  }
}
