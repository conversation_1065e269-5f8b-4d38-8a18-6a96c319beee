'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Quote, 
  Star, 
  ChevronLeft, 
  ChevronRight, 
  Users,
  Award,
  ThumbsUp
} from 'lucide-react';
import Image from 'next/image';
import { luxuryAnimations, initGSAP } from '@/lib/animations/gsap';
import { cn } from '@/lib/utils/cn';

interface TestimonialProps {
  id: string;
  name: string;
  title: string;
  company?: string;
  avatar: string;
  content: string;
  rating: number;
  date: string;
  verified: boolean;
  propertyType?: string;
}

const TestimonialCard: React.FC<{ 
  testimonial: TestimonialProps; 
  isActive: boolean;
}> = ({ testimonial, isActive }) => {
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const node = cardRef.current;
    if (!node) return;

    const hoverAnimation = luxuryAnimations.luxuryHover(node);
    const handleMouseEnter = () => hoverAnimation.play();
    const handleMouseLeave = () => hoverAnimation.reverse();

    node.addEventListener('mouseenter', handleMouseEnter);
    node.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      node.removeEventListener('mouseenter', handleMouseEnter);
      node.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          "w-4 h-4",
          i < rating 
            ? "text-luxury-gold fill-luxury-gold" 
            : "text-luxury-gold/30"
        )}
      />
    ));
  };

  return (
    <Card 
      ref={cardRef}
      className={cn(
        "luxury-glass backdrop-blur-sm p-8 relative transition-all duration-700",
        "border border-luxury-gold/20 hover:border-luxury-gold/40",
        isActive 
          ? "scale-105 shadow-2xl shadow-luxury-gold/20" 
          : "scale-95 opacity-80",
        "hover:shadow-2xl"
      )}
    >
      {/* Quote Icon */}
      <div className="absolute -top-4 left-8">
        <div className="w-8 h-8 bg-gradient-to-br from-luxury-gold to-luxury-gold-light rounded-full flex items-center justify-center">
          <Quote className="w-4 h-4 text-white" />
        </div>
      </div>

      {/* Verified Badge */}
      {testimonial.verified && (
        <div className="absolute -top-2 -right-2">
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
            <ThumbsUp className="w-4 h-4 text-white" />
          </div>
        </div>
      )}

      {/* Rating */}
      <div className="flex items-center gap-1 mb-4">
        {renderStars(testimonial.rating)}
        <span className="text-luxury-gold text-sm font-medium ml-2">
          {testimonial.rating}/5
        </span>
      </div>

      {/* Content */}
      <blockquote className="luxury-body text-white/90 text-lg leading-relaxed mb-6 italic">
        "{testimonial.content}"
      </blockquote>

      {/* Author Info */}
      <div className="flex items-center gap-4">
        <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-luxury-gold to-luxury-gold-light flex-shrink-0">
          <Image
            src={testimonial.avatar}
            alt={testimonial.name}
            width={48}
            height={48}
            className="w-full h-full object-cover"
            onError={(e: React.SyntheticEvent<HTMLImageElement, Event>) => {
              // Fallback to initials if image fails
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const parent = target.parentElement;
              if (parent) {
                parent.innerHTML = `<div class="w-full h-full flex items-center justify-center text-white font-semibold">${testimonial.name.split(' ').map(n => n[0]).join('')}</div>`;
              }
            }}
          />
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className="luxury-subheading text-white font-semibold">
            {testimonial.name}
          </h4>
          <p className="luxury-caption text-white/70 text-sm">
            {testimonial.title}
            {testimonial.company && (
              <span className="text-luxury-gold"> • {testimonial.company}</span>
            )}
          </p>
          {testimonial.propertyType && (
            <p className="luxury-caption text-luxury-gold/80 text-xs mt-1">
              {testimonial.propertyType} Owner
            </p>
          )}
        </div>

        <div className="text-right">
          <p className="luxury-caption text-white/50 text-xs">
            {testimonial.date}
          </p>
        </div>
      </div>
    </Card>
  );
};

const TestimonialsSection: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const carouselRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const testimonials: TestimonialProps[] = [
    {
      id: "1",
      name: "Rajesh Kumar",
      title: "Senior Manager",
      company: "Tata Steel",
      avatar: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg",
      content: "The Sixteen has exceeded every expectation. The luxury finishes, attention to detail, and prime location make it the perfect investment. Living here feels like being in a 5-star hotel every day.",
      rating: 5,
      date: "December 2024",
      verified: true,
      propertyType: "3BHK Penthouse"
    },
    {
      id: "2",
      name: "Priya Sharma",
      title: "Professor",
      company: "NIT Rourkela",
      avatar: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg",
      content: "As an academic, I value the proximity to NIT and the serene environment. The amenities are world-class, and the community is sophisticated. It's Rourkela's finest residential address.",
      rating: 5,
      date: "November 2024",
      verified: true,
      propertyType: "2BHK Luxury"
    },
    {
      id: "3",
      name: "Amit Patel",
      title: "Business Owner", 
      company: "Steel Trading Corp",
      avatar: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg",
      content: 'The investment value and appreciation potential are outstanding. The build quality is impeccable, and the smart home features are truly impressive. Highly recommend to anyone seeking luxury.',
      rating: 5,
      date: "October 2024",
      verified: true,
      propertyType: "4BHK Duplex"
    },
    {
      id: "4",
      name: "Sneha Mohanty",
      title: "Doctor",
      company: "AIIMS Bhubaneswar",
      avatar: "https://images.pexels.com/photos/1181519/pexels-photo-1181519.jpeg",
      content: "The safety features and medical facilities nearby gave me confidence. The luxury amenities like the spa and fitness center are perfect for my lifestyle. It's truly a premium living experience.",
      rating: 5,
      date: "September 2024",
      verified: true,
      propertyType: "3BHK Premium"
    }
  ];

  useEffect(() => {
    initGSAP();

    if (headerRef.current) {
      luxuryAnimations.fadeInUp(headerRef.current, 0.2);
    }

    if (carouselRef.current) {
      luxuryAnimations.fadeInUp(carouselRef.current, 0.4);
    }

    if (statsRef.current) {
      luxuryAnimations.staggerFadeIn(statsRef.current.children, 0.1);
    }
  }, []);

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, testimonials.length]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  return (
    <section 
      ref={sectionRef}
      id="testimonials"
      className="relative py-20 lg:py-32 bg-luxury-charcoal-light overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-luxury-charcoal-light via-luxury-charcoal to-luxury-charcoal-dark" />
      <div className="absolute top-0 right-0 w-full h-full opacity-5 bg-[url('https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=1920')] bg-cover bg-center" />
      
      {/* Decorative Elements */}
      <div className="absolute top-20 left-10 w-2 h-32 bg-gradient-to-b from-luxury-gold to-transparent opacity-60" />
      <div className="absolute bottom-20 right-10 w-2 h-32 bg-gradient-to-t from-luxury-gold to-transparent opacity-60" />

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div ref={headerRef} className="text-center mb-16">
          <div className="inline-flex items-center gap-2 luxury-glass rounded-full px-6 py-3 mb-6 backdrop-blur-sm">
            <Users className="w-4 h-4 text-luxury-gold" />
            <span className="luxury-caption text-white">Client Reviews</span>
          </div>
          
          <h2 className="luxury-heading text-4xl md:text-6xl font-bold text-white mb-6">
            What Our{' '}
            <span className="luxury-gradient-text">Residents</span>
            {' '}Say
          </h2>
          
          <p className="luxury-subheading text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Discover why discerning residents choose The Sixteen as their luxury home address
          </p>
        </div>

        {/* Testimonials Carousel */}
        <div ref={carouselRef} className="relative mb-16">
          <div className="flex items-center justify-center gap-8">
            {/* Previous Button */}
            <Button
              variant="luxuryOutline"
              size="icon"
              onClick={prevTestimonial}
              className="flex-shrink-0 w-12 h-12 rounded-full hover:scale-110 transition-transform"
            >
              <ChevronLeft className="w-5 h-5" />
            </Button>

            {/* Active Testimonial */}
            <div className="flex-1 max-w-4xl">
              {testimonials[currentIndex] && (
                <TestimonialCard
                  testimonial={testimonials[currentIndex]}
                  isActive={true}
                />
              )}
            </div>

            {/* Next Button */}
            <Button
              variant="luxuryOutline"
              size="icon"
              onClick={nextTestimonial}
              className="flex-shrink-0 w-12 h-12 rounded-full hover:scale-110 transition-transform"
            >
              <ChevronRight className="w-5 h-5" />
            </Button>
          </div>

          {/* Carousel Indicators */}
          <div className="flex justify-center gap-3 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={cn(
                  "w-3 h-3 rounded-full transition-all duration-300",
                  index === currentIndex
                    ? "bg-luxury-gold scale-125"
                    : "bg-luxury-gold/30 hover:bg-luxury-gold/60"
                )}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>

        {/* Testimonial Stats */}
        <div ref={statsRef} className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <Card className="luxury-glass backdrop-blur-sm p-8 text-center border border-luxury-gold/20">
            <Award className="w-12 h-12 text-luxury-gold mx-auto mb-4" />
            <div className="luxury-heading text-3xl font-bold text-white mb-2">4.9/5</div>
            <div className="luxury-body text-white/70">Average Rating</div>
          </Card>
          
          <Card className="luxury-glass backdrop-blur-sm p-8 text-center border border-luxury-gold/20">
            <Users className="w-12 h-12 text-luxury-gold mx-auto mb-4" />
            <div className="luxury-heading text-3xl font-bold text-white mb-2">250+</div>
            <div className="luxury-body text-white/70">Happy Residents</div>
          </Card>
          
          <Card className="luxury-glass backdrop-blur-sm p-8 text-center border border-luxury-gold/20">
            <ThumbsUp className="w-12 h-12 text-luxury-gold mx-auto mb-4" />
            <div className="luxury-heading text-3xl font-bold text-white mb-2">98%</div>
            <div className="luxury-body text-white/70">Satisfaction Rate</div>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <p className="luxury-body text-white/80 mb-6">
            Join our community of satisfied residents
          </p>
          <Button variant="luxury" size="xl" className="luxury-shine-enhanced">
            <Award className="w-5 h-5" />
            Schedule Your Visit
          </Button>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;