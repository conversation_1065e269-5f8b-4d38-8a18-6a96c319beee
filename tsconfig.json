{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./app/*"], "@/components/*": ["./app/components/*"], "@/lib/*": ["./app/lib/*"], "@/data/*": ["./app/data/*"], "@/types/*": ["./app/types/*"], "@/ui/*": ["./app/components/ui/*"], "@/animations/*": ["./app/lib/animations/*"], "@/utils/*": ["./app/lib/utils/*"]}, "forceConsistentCasingInFileNames": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "app/**/*.ts", "app/**/*.tsx", "app/types/**/*.d.ts"], "exclude": ["node_modules", ".next", "out"]}