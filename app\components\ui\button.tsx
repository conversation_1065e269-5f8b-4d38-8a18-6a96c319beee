import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils/cn";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline: "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        // Enhanced luxury variants with proper contrast ratios
        luxury: "bg-luxury-gold text-white border-2 border-luxury-gold hover:bg-luxury-gold-dark hover:border-luxury-gold-dark shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 font-semibold",
        luxuryOutline: "border-2 border-luxury-gold text-luxury-gold bg-white hover:bg-luxury-gold hover:text-white shadow-sm hover:shadow-lg transition-all duration-300 font-medium",
        luxurySecondary: "border-2 border-luxury-gold text-luxury-gold bg-transparent hover:bg-luxury-gold/10 hover:border-luxury-gold-dark shadow-sm hover:shadow-md transition-all duration-300 font-medium",
        luxuryDark: "bg-luxury-charcoal text-white hover:bg-luxury-charcoal-light shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 font-medium",
        luxuryGhost: "text-luxury-gold hover:bg-luxury-gold/10 hover:text-luxury-gold-dark transition-all duration-300 font-medium",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        xl: "h-12 rounded-lg px-10 text-base",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };