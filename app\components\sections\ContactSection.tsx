'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select } from '@/components/ui/select';
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  Send, 
  Calendar,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  User,
  Home,
  Users
} from 'lucide-react';
import { luxuryAnimations, initGSAP } from '@/lib/animations/gsap';
import { cn } from '@/lib/utils/cn';
import type { ContactForm } from '@/data/types';

interface FormState {
  isSubmitting: boolean;
  isSubmitted: boolean;
  error: string | null;
}

const ContactInfoCard: React.FC<{
  icon: React.ReactNode;
  title: string;
  primary: string;
  secondary?: string;
  action?: string;
}> = ({ icon, title, primary, secondary, action }) => {
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (cardRef.current) {
      const hoverAnimation = luxuryAnimations.luxuryHover(cardRef.current);
      
      const handleMouseEnter = () => hoverAnimation.play();
      const handleMouseLeave = () => hoverAnimation.reverse();
      
      cardRef.current.addEventListener('mouseenter', handleMouseEnter);
      cardRef.current.addEventListener('mouseleave', handleMouseLeave);
      
      return () => {
        if (cardRef.current) {
          cardRef.current.removeEventListener('mouseenter', handleMouseEnter);
          cardRef.current.removeEventListener('mouseleave', handleMouseLeave);
        }
      };
    }
    return undefined;
  }, [luxuryAnimations]);

  return (
    <Card 
      ref={cardRef}
      className="luxury-glass backdrop-blur-sm p-6 text-center border border-luxury-gold/20 hover:border-luxury-gold/40 transition-all duration-500"
    >
      <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-luxury-gold to-luxury-gold-light rounded-2xl flex items-center justify-center">
        {icon}
      </div>
      
      <h3 className="luxury-subheading text-lg font-semibold text-white mb-2">
        {title}
      </h3>
      
      <p className="luxury-body text-white/80 mb-1">
        {primary}
      </p>
      
      {secondary && (
        <p className="luxury-caption text-white/60 text-sm">
          {secondary}
        </p>
      )}
      
      {action && (
        <Button variant="luxuryOutline" size="sm" className="mt-4">
          {action}
        </Button>
      )}
    </Card>
  );
};

const ContactSection: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const contactInfoRef = useRef<HTMLDivElement>(null);
  const formsRef = useRef<HTMLDivElement>(null);

  const [activeForm, setActiveForm] = useState<'contact' | 'visit'>('contact');
  const [contactFormData, setContactFormData] = useState<ContactForm>({
    name: '',
    email: '',
    phone: '',
    message: '',
    preferredContact: 'email',
    bestTimeToContact: ''
  });
  
  const [visitFormData, setVisitFormData] = useState({
    name: '',
    email: '',
    phone: '',
    preferredDate: '',
    preferredTime: '',
    groupSize: '1',
    interests: 'general',
    message: ''
  });

  const [contactFormState, setContactFormState] = useState<FormState>({
    isSubmitting: false,
    isSubmitted: false,
    error: null
  });

  const [visitFormState, setVisitFormState] = useState<FormState>({
    isSubmitting: false,
    isSubmitted: false,
    error: null
  });

  useEffect(() => {
    initGSAP();

    if (headerRef.current) {
      luxuryAnimations.fadeInUp(headerRef.current, 0.2);
    }

    if (contactInfoRef.current) {
      luxuryAnimations.staggerFadeIn(contactInfoRef.current.children, 0.1);
    }

    if (formsRef.current) {
      luxuryAnimations.fadeInUp(formsRef.current, 0.6);
    }
  }, []);

  const handleContactSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setContactFormState({ isSubmitting: true, isSubmitted: false, error: null });

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setContactFormState({ isSubmitting: false, isSubmitted: true, error: null });
      
      // Reset form after success
      setTimeout(() => {
        setContactFormData({
          name: '',
          email: '',
          phone: '',
          message: '',
          preferredContact: 'email',
          bestTimeToContact: ''
        });
        setContactFormState({ isSubmitting: false, isSubmitted: false, error: null });
      }, 3000);
      
    } catch (error: any) {
      setContactFormState({ 
        isSubmitting: false, 
        isSubmitted: false, 
        error: 'Failed to send message. Please try again.' 
      });
    }
  };

  const handleVisitSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setVisitFormState({ isSubmitting: true, isSubmitted: false, error: null });

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setVisitFormState({ isSubmitting: false, isSubmitted: true, error: null });
      
      // Reset form after success
      setTimeout(() => {
        setVisitFormData({
          name: '',
          email: '',
          phone: '',
          preferredDate: '',
          preferredTime: '',
          groupSize: '1',
          interests: 'general',
          message: ''
        });
        setVisitFormState({ isSubmitting: false, isSubmitted: false, error: null });
      }, 3000);
      
    } catch (error: any) {
      setVisitFormState({ 
        isSubmitting: false, 
        isSubmitted: false, 
        error: 'Failed to schedule visit. Please try again.' 
      });
    }
  };

  const contactInfo = [
    {
      icon: <Phone className="w-8 h-8 text-white" />,
      title: "Call Us",
      primary: "+91 1234 567 890",
      secondary: "Mon - Sat, 9 AM - 8 PM",
      action: "Call Now"
    },
    {
      icon: <Mail className="w-8 h-8 text-white" />,
      title: "Email Us", 
      primary: "<EMAIL>",
      secondary: "We'll respond within 24 hours",
      action: "Send Email"
    },
    {
      icon: <MapPin className="w-8 h-8 text-white" />,
      title: "Visit Us",
      primary: "Sector 6, Rourkela",
      secondary: "Experience Center Open Daily",
      action: "Get Directions"
    },
    {
      icon: <Clock className="w-8 h-8 text-white" />,
      title: "Working Hours",
      primary: "Mon - Sat: 9 AM - 8 PM",
      secondary: "Sun: 10 AM - 6 PM"
    }
  ];

  return (
    <section 
      ref={sectionRef}
      id="contact"
      className="relative py-20 lg:py-32 bg-luxury-charcoal overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-luxury-charcoal via-luxury-charcoal-light to-luxury-charcoal" />
      <div className="absolute top-0 left-0 w-full h-full opacity-5 bg-[url('https://images.pexels.com/photos/323772/pexels-photo-323772.jpeg?auto=compress&cs=tinysrgb&w=1920')] bg-cover bg-center" />
      
      {/* Decorative Elements */}
      <div className="absolute top-20 right-10 w-2 h-32 bg-gradient-to-b from-luxury-gold to-transparent opacity-60" />
      <div className="absolute bottom-20 left-10 w-2 h-32 bg-gradient-to-t from-luxury-gold to-transparent opacity-60" />

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div ref={headerRef} className="text-center mb-16">
          <div className="inline-flex items-center gap-2 luxury-glass rounded-full px-6 py-3 mb-6 backdrop-blur-sm">
            <MessageSquare className="w-4 h-4 text-luxury-gold" />
            <span className="luxury-caption text-white">Get In Touch</span>
          </div>
          
          <h2 className="luxury-heading text-4xl md:text-6xl font-bold text-white mb-6">
            Contact{' '}
            <span className="luxury-gradient-text">The Sixteen</span>
          </h2>
          
          <p className="luxury-subheading text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Ready to make The Sixteen your home? Our luxury living consultants are here to assist you.
          </p>
        </div>

        {/* Contact Information Cards */}
        <div 
          ref={contactInfoRef}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {contactInfo.map((info, index) => (
            <ContactInfoCard key={index} {...info} />
          ))}
        </div>

        {/* Contact Forms */}
        <div ref={formsRef} className="max-w-6xl mx-auto">
          {/* Form Tabs */}
          <div className="flex justify-center mb-8">
            <div className="luxury-glass rounded-full p-2 backdrop-blur-sm">
              <div className="flex gap-2">
                <Button
                  variant={activeForm === 'contact' ? 'luxury' : 'ghost'}
                  onClick={() => setActiveForm('contact')}
                  className={cn(
                    "rounded-full transition-all duration-300",
                    activeForm === 'contact' 
                      ? "shadow-lg shadow-luxury-gold/25" 
                      : "text-white/70 hover:text-white"
                  )}
                >
                  <MessageSquare className="w-4 h-4" />
                  General Inquiry
                </Button>
                <Button
                  variant={activeForm === 'visit' ? 'luxury' : 'ghost'}
                  onClick={() => setActiveForm('visit')}
                  className={cn(
                    "rounded-full transition-all duration-300",
                    activeForm === 'visit' 
                      ? "shadow-lg shadow-luxury-gold/25" 
                      : "text-white/70 hover:text-white"
                  )}
                >
                  <Calendar className="w-4 h-4" />
                  Schedule Visit
                </Button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            {activeForm === 'contact' && (
              <Card className="luxury-glass backdrop-blur-sm p-8 border border-luxury-gold/20">
                <div className="flex items-center gap-3 mb-6">
                  <MessageSquare className="w-6 h-6 text-luxury-gold" />
                  <h3 className="luxury-subheading text-2xl font-semibold text-white">
                    Send us a Message
                  </h3>
                </div>

                <form onSubmit={handleContactSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="luxury-caption text-white/80 mb-2 block">
                        Full Name *
                      </label>
                      <Input
                        type="text"
                        required
                        value={contactFormData.name}
                        onChange={(e) => setContactFormData({...contactFormData, name: e.target.value})}
                        className="bg-luxury-charcoal-light border-luxury-gold/20 text-white placeholder:text-white/50"
                        placeholder="Enter your full name"
                      />
                    </div>
                    
                    <div>
                      <label className="luxury-caption text-white/80 mb-2 block">
                        Email Address *
                      </label>
                      <Input
                        type="email"
                        required
                        value={contactFormData.email}
                        onChange={(e) => setContactFormData({...contactFormData, email: e.target.value})}
                        className="bg-luxury-charcoal-light border-luxury-gold/20 text-white placeholder:text-white/50"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="luxury-caption text-white/80 mb-2 block">
                      Phone Number
                    </label>
                    <Input
                      type="tel"
                      value={contactFormData.phone}
                      onChange={(e) => setContactFormData({...contactFormData, phone: e.target.value})}
                      className="bg-luxury-charcoal-light border-luxury-gold/20 text-white placeholder:text-white/50"
                      placeholder="+91 12345 67890"
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="luxury-caption text-white/80 mb-2 block">
                        Preferred Contact Method
                      </label>
                      <select
                        value={contactFormData.preferredContact}
                        onChange={(e) => setContactFormData({...contactFormData, preferredContact: e.target.value as 'email' | 'phone' | 'text'})}
                        className="w-full px-3 py-2 bg-luxury-charcoal-light border border-luxury-gold/20 rounded-md text-white"
                      >
                        <option value="email">Email</option>
                        <option value="phone">Phone Call</option>
                        <option value="text">Text Message</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="luxury-caption text-white/80 mb-2 block">
                        Best Time to Contact
                      </label>
                      <Input
                        type="text"
                        value={contactFormData.bestTimeToContact}
                        onChange={(e) => setContactFormData({...contactFormData, bestTimeToContact: e.target.value})}
                        className="bg-luxury-charcoal-light border-luxury-gold/20 text-white placeholder:text-white/50"
                        placeholder="e.g., Morning, Evening"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="luxury-caption text-white/80 mb-2 block">
                      Message *
                    </label>
                    <Textarea
                      required
                      rows={4}
                      value={contactFormData.message}
                      onChange={(e) => setContactFormData({...contactFormData, message: e.target.value})}
                      className="bg-luxury-charcoal-light border-luxury-gold/20 text-white placeholder:text-white/50 resize-none"
                      placeholder="Tell us about your requirements, budget, or any questions you have..."
                    />
                  </div>

                  {contactFormState.error && (
                    <div className="flex items-center gap-2 text-red-400 bg-red-400/10 p-3 rounded-lg">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">{contactFormState.error}</span>
                    </div>
                  )}

                  {contactFormState.isSubmitted && (
                    <div className="flex items-center gap-2 text-green-400 bg-green-400/10 p-3 rounded-lg">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">Message sent successfully! We&apos;ll get back to you soon.</span>
                    </div>
                  )}

                  <Button
                    type="submit"
                    variant="luxury"
                    size="lg"
                    disabled={contactFormState.isSubmitting}
                    className="w-full luxury-shine-enhanced"
                  >
                    {contactFormState.isSubmitting ? (
                      "Sending..."
                    ) : (
                      <>
                        <Send className="w-4 h-4" />
                        Send Message
                      </>
                    )}
                  </Button>
                </form>
              </Card>
            )}

            {/* Visit Scheduling Form */}
            {activeForm === 'visit' && (
              <Card className="luxury-glass backdrop-blur-sm p-8 border border-luxury-gold/20">
                <div className="flex items-center gap-3 mb-6">
                  <Calendar className="w-6 h-6 text-luxury-gold" />
                  <h3 className="luxury-subheading text-2xl font-semibold text-white">
                    Schedule Your Visit
                  </h3>
                </div>

                <form onSubmit={handleVisitSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="luxury-caption text-white/80 mb-2 block">
                        Full Name *
                      </label>
                      <Input
                        type="text"
                        required
                        value={visitFormData.name}
                        onChange={(e) => setVisitFormData({...visitFormData, name: e.target.value})}
                        className="bg-luxury-charcoal-light border-luxury-gold/20 text-white placeholder:text-white/50"
                        placeholder="Enter your full name"
                      />
                    </div>
                    
                    <div>
                      <label className="luxury-caption text-white/80 mb-2 block">
                        Email Address *
                      </label>
                      <Input
                        type="email"
                        required
                        value={visitFormData.email}
                        onChange={(e) => setVisitFormData({...visitFormData, email: e.target.value})}
                        className="bg-luxury-charcoal-light border-luxury-gold/20 text-white placeholder:text-white/50"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="luxury-caption text-white/80 mb-2 block">
                      Phone Number *
                    </label>
                    <Input
                      type="tel"
                      required
                      value={visitFormData.phone}
                      onChange={(e) => setVisitFormData({...visitFormData, phone: e.target.value})}
                      className="bg-luxury-charcoal-light border-luxury-gold/20 text-white placeholder:text-white/50"
                      placeholder="+91 12345 67890"
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="luxury-caption text-white/80 mb-2 block">
                        Preferred Date *
                      </label>
                      <Input
                        type="date"
                        required
                        value={visitFormData.preferredDate}
                        onChange={(e) => setVisitFormData({...visitFormData, preferredDate: e.target.value})}
                        className="bg-luxury-charcoal-light border-luxury-gold/20 text-white"
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>
                    
                    <div>
                      <label className="luxury-caption text-white/80 mb-2 block">
                        Preferred Time *
                      </label>
                      <select
                        required
                        value={visitFormData.preferredTime}
                        onChange={(e) => setVisitFormData({...visitFormData, preferredTime: e.target.value})}
                        className="w-full px-3 py-2 bg-luxury-charcoal-light border border-luxury-gold/20 rounded-md text-white"
                      >
                        <option value="">Select time</option>
                        <option value="9:00 AM">9:00 AM</option>
                        <option value="11:00 AM">11:00 AM</option>
                        <option value="2:00 PM">2:00 PM</option>
                        <option value="4:00 PM">4:00 PM</option>
                        <option value="6:00 PM">6:00 PM</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="luxury-caption text-white/80 mb-2 block">
                        Group Size
                      </label>
                      <select
                        value={visitFormData.groupSize}
                        onChange={(e) => setVisitFormData({...visitFormData, groupSize: e.target.value})}
                        className="w-full px-3 py-2 bg-luxury-charcoal-light border border-luxury-gold/20 rounded-md text-white"
                      >
                        <option value="1">Just me</option>
                        <option value="2">2 people</option>
                        <option value="3-5">3-5 people</option>
                        <option value="6+">6+ people</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="luxury-caption text-white/80 mb-2 block">
                        Interest Area
                      </label>
                      <select
                        value={visitFormData.interests}
                        onChange={(e) => setVisitFormData({...visitFormData, interests: e.target.value})}
                        className="w-full px-3 py-2 bg-luxury-charcoal-light border border-luxury-gold/20 rounded-md text-white"
                      >
                        <option value="general">General Tour</option>
                        <option value="2bhk">2BHK Units</option>
                        <option value="3bhk">3BHK Units</option>
                        <option value="4bhk">4BHK Units</option>
                        <option value="penthouse">Penthouse</option>
                        <option value="amenities">Amenities Focus</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="luxury-caption text-white/80 mb-2 block">
                      Additional Notes
                    </label>
                    <Textarea
                      rows={3}
                      value={visitFormData.message}
                      onChange={(e) => setVisitFormData({...visitFormData, message: e.target.value})}
                      className="bg-luxury-charcoal-light border-luxury-gold/20 text-white placeholder:text-white/50 resize-none"
                      placeholder="Any specific requirements or questions for your visit?"
                    />
                  </div>

                  {visitFormState.error && (
                    <div className="flex items-center gap-2 text-red-400 bg-red-400/10 p-3 rounded-lg">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">{visitFormState.error}</span>
                    </div>
                  )}

                  {visitFormState.isSubmitted && (
                    <div className="flex items-center gap-2 text-green-400 bg-green-400/10 p-3 rounded-lg">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">Visit scheduled successfully! We&apos;ll send you a confirmation.</span>
                    </div>
                  )}

                  <Button
                    type="submit"
                    variant="luxury"
                    size="lg"
                    disabled={visitFormState.isSubmitting}
                    className="w-full luxury-shine-enhanced"
                  >
                    {visitFormState.isSubmitting ? (
                      "Scheduling..."
                    ) : (
                      <>
                        <Calendar className="w-4 h-4" />
                        Schedule Visit
                      </>
                    )}
                  </Button>
                </form>
              </Card>
            )}

            {/* Contact Information Side */}
            <div className="space-y-8">
              {/* Experience Center */}
              <Card className="luxury-glass backdrop-blur-sm p-8 border border-luxury-gold/20">
                <div className="flex items-center gap-3 mb-6">
                  <Home className="w-6 h-6 text-luxury-gold" />
                  <h3 className="luxury-subheading text-2xl font-semibold text-white">
                    Experience Center
                  </h3>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-luxury-gold mt-1 flex-shrink-0" />
                    <div>
                      <p className="luxury-body text-white">
                        The Sixteen Sales Office<br />
                        Sector 6, Rourkela, Odisha 769001
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Clock className="w-5 h-5 text-luxury-gold" />
                    <p className="luxury-body text-white/80">
                      Open Daily: 9 AM - 8 PM
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Users className="w-5 h-5 text-luxury-gold" />
                    <p className="luxury-body text-white/80">
                      Luxury Living Consultants Available
                    </p>
                  </div>
                </div>
                
                <div className="mt-6 pt-6 border-t border-luxury-gold/20">
                  <p className="luxury-caption text-white/70 text-sm mb-4">
                    Visit our state-of-the-art experience center to explore sample units, 
                    view architectural models, and discuss customization options.
                  </p>
                  
                  <Button variant="luxuryOutline" size="sm" className="w-full">
                    <MapPin className="w-4 h-4" />
                    Get Directions
                  </Button>
                </div>
              </Card>

              {/* Quick Stats */}
              <Card className="luxury-glass backdrop-blur-sm p-8 border border-luxury-gold/20">
                <h4 className="luxury-subheading text-lg font-semibold text-white mb-6">
                  Why Choose The Sixteen?
                </h4>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="luxury-body text-white/80">Response Time</span>
                    <span className="luxury-body text-luxury-gold font-semibold">2 Hours</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="luxury-body text-white/80">Customer Satisfaction</span>
                    <span className="luxury-body text-luxury-gold font-semibold">98%</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="luxury-body text-white/80">Units Available</span>
                    <span className="luxury-body text-luxury-gold font-semibold">45+</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="luxury-body text-white/80">Financing Options</span>
                    <span className="luxury-body text-luxury-gold font-semibold">Available</span>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;