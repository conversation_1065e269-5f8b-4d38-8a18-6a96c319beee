// Animation exports
export * from './gsap';
export * from './framer';

// Common animation utilities
export const ANIMATION_DURATION = {
  fast: 0.2,
  normal: 0.4,
  slow: 0.8,
  luxury: 1.2,
} as const;

export const EASING = {
  luxury: [0.6, 0.01, 0.05, 0.95],
  smooth: [0.4, 0, 0.2, 1],
  bounce: [0.68, -0.55, 0.265, 1.55],
} as const;

export type AnimationDuration = keyof typeof ANIMATION_DURATION;
export type EasingType = keyof typeof EASING;