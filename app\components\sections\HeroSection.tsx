'use client';

import Image from 'next/image';
import React, { useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronDown, Sparkles, Building2, Users, Award, Star } from 'lucide-react';
import { luxuryAnimations, initGSAP } from '@/lib/animations/gsap';
import { gsap } from 'gsap';

interface StatisticProps {
  number: number;
  label: string;
  suffix?: string;
  delay?: number;
  icon: React.ReactNode;
}

const StatisticCard: React.FC<StatisticProps> = ({ number, label, suffix = '', icon }) => {
  const counterRef = useRef<HTMLSpanElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (counterRef.current) {
        luxuryAnimations.countUp(counterRef.current, number, 2.5);
      }
    }, 1500); // Delay counter animation
    
    return () => clearTimeout(timer);
  }, [number]);

  return (
    <div 
      ref={cardRef}
      className="text-center luxury-glass-enhanced rounded-xl p-6 backdrop-blur-lg stats-card transition-all duration-500 hover:scale-105 group border border-luxury-gold/20 hover:border-luxury-gold/40"
    >
      <div className="w-12 h-12 mx-auto mb-4 bg-luxury-gold/20 rounded-full flex items-center justify-center group-hover:bg-luxury-gold/30 transition-colors duration-300">
        {icon}
      </div>
      <div className="luxury-heading text-2xl md:text-3xl luxury-gradient-text mb-2 font-bold">
        <span ref={counterRef}>0</span>
        {suffix && <span className="text-luxury-gold">{suffix}</span>}
      </div>
      <div className="luxury-caption text-white/90 font-medium">{label}</div>
    </div>
  );
};

const HeroSection: React.FC = () => {
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);
  const scrollIndicatorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Initialize GSAP
    initGSAP();

    // 8-step luxury entrance animation timeline
    const masterTimeline = gsap.timeline({
      defaults: { ease: 'power3.out' }
    });

    // Step 1: Background reveal with parallax
    masterTimeline.fromTo(
      heroRef.current,
      { opacity: 0 },
      { opacity: 1, duration: 0.8 }
    )
    // Step 2: Premium badge entrance
    .fromTo(
      '.premium-badge',
      { opacity: 0, y: -30, scale: 0.8 },
      { opacity: 1, y: 0, scale: 1, duration: 0.6 },
      '-=0.4'
    )
    // Step 3: Main title with dramatic reveal
    .fromTo(
      titleRef.current,
      { opacity: 0, y: 80, skewY: 5 },
      { opacity: 1, y: 0, skewY: 0, duration: 1.2 },
      '-=0.2'
    )
    // Step 4: Subtitle with elegant entrance
    .fromTo(
      subtitleRef.current,
      { opacity: 0, y: 40, x: -20 },
      { opacity: 1, y: 0, x: 0, duration: 0.8 },
      '-=0.6'
    )
    // Step 5: Statistics cards with stagger effect
    .fromTo(
      statsRef.current?.children || [],
      { opacity: 0, y: 50, rotateY: 45 },
      { opacity: 1, y: 0, rotateY: 0, duration: 0.8, stagger: 0.15 },
      '-=0.4'
    )
    // Step 6: CTA buttons with bounce
    .fromTo(
      ctaRef.current,
      { opacity: 0, scale: 0.8, y: 30 },
      { opacity: 1, scale: 1, y: 0, duration: 0.6, ease: 'back.out(1.7)' },
      '-=0.2'
    )
    // Step 7: Achievement badge with slide in
    .fromTo(
      '.achievement-badge',
      { opacity: 0, x: 30 },
      { opacity: 1, x: 0, duration: 0.6 },
      '-=0.4'
    )
    // Step 8: Scroll indicator with final flourish
    .fromTo(
      scrollIndicatorRef.current,
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.5 },
      '-=0.2'
    );

    return () => {
      masterTimeline.kill();
    };
  }, []);

  const handleScrollToNext = () => {
    const nextSection = document.querySelector('#about') || document.querySelector('section:nth-of-type(2)');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  

  return (
    <section 
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-luxury-dark"
    >
      {/* Background Image with Parallax */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/hero-section-image.webp"
          alt="Luxury building exterior"
          layout="fill"
          objectFit="cover"
          quality={100}
          className="hero-background z-[-1]"
        />
      </div>

      {/* Experience Badge */}
      <div className="absolute top-8 right-8 z-20 luxury-glass rounded-full px-6 py-3 backdrop-blur-sm">
        <div className="flex items-center gap-2">
          <Award className="w-5 h-5 text-luxury-gold" />
          <span className="luxury-caption text-white font-medium">Premium Experience</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 container mx-auto px-6 text-center pt-24">
        {/* Premium Badge */}
        <div className="premium-badge inline-flex items-center gap-2 luxury-glass-enhanced rounded-full px-8 py-4 mb-8 backdrop-blur-lg border border-luxury-gold/30 hover:border-luxury-gold/50 transition-all duration-300">
          <Sparkles className="w-5 h-5 text-luxury-gold animate-pulse" />
          <span className="luxury-caption text-white font-semibold tracking-wider">Rourkela&apos;s Premier Luxury Development</span>
          <Sparkles className="w-5 h-5 text-luxury-gold animate-pulse" />
        </div>

        {/* Main Headline */}
        <h1 
          ref={titleRef}
          className="luxury-heading text-5xl md:text-7xl lg:text-9xl font-black text-white mb-6 leading-[0.9] hero-text-glow tracking-tight"
        >
          The{' '}
          <span className="luxury-gradient-text font-playfair italic relative">
            Sixteen
            <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-luxury-gold to-transparent opacity-60"></div>
          </span>
        </h1>

        {/* Subtitle */}
        <div 
          ref={subtitleRef}
          className="mb-12 max-w-5xl mx-auto"
        >
          <p className="luxury-subheading text-xl md:text-2xl lg:text-3xl text-white/95 mb-4 leading-relaxed font-light">
            Rourkela&apos;s Pinnacle of Luxury Living
          </p>
          <p className="text-luxury-gold font-semibold text-lg md:text-xl lg:text-2xl tracking-wide">
            16 Floors of Unparalleled Elegance
          </p>
          <div className="mt-6 w-24 h-0.5 bg-gradient-to-r from-luxury-gold to-luxury-gold-light mx-auto"></div>
        </div>

        {/* Statistics */}
        <div 
          ref={statsRef}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16 max-w-4xl mx-auto"
        >
          <StatisticCard 
            number={16} 
            label="Floors of Luxury"
            icon={<Building2 className="w-6 h-6 text-luxury-gold" />}
            delay={0.2}
          />
          <StatisticCard 
            number={120} 
            label="Premium Amenities"
            suffix="+"
            icon={<Star className="w-6 h-6 text-luxury-gold" />}
            delay={0.4}
          />
          <StatisticCard 
            number={5} 
            label="Star Experience"
            icon={<Award className="w-6 h-6 text-luxury-gold" />}
            delay={0.6}
          />
        </div>

        {/* Call to Action */}
        <div ref={ctaRef} className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
          <Button 
            variant="luxury" 
            size="xl"
            className="luxury-shine-enhanced group px-12 py-4 text-lg font-semibold"
          >
            <Building2 className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
            Explore Residences
          </Button>
          
          <Button 
            variant="luxuryOutline" 
            size="xl"
            className="luxury-shine-enhanced group px-12 py-4 text-lg font-semibold"
          >
            <Users className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
            Schedule Visit
          </Button>
        </div>

        {/* Achievement Badge */}
        <div className="achievement-badge inline-flex items-center gap-3 luxury-glass-enhanced rounded-full px-8 py-4 backdrop-blur-lg border border-luxury-gold/20">
          <Award className="w-5 h-5 text-luxury-gold" />
          <span className="luxury-body text-white/95 font-medium">
            Winner - Best Luxury Development 2024
          </span>
          <div className="w-2 h-2 bg-luxury-gold rounded-full animate-pulse"></div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div 
        ref={scrollIndicatorRef}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10"
      >
        <button
          onClick={handleScrollToNext}
          className="flex flex-col items-center gap-2 group cursor-pointer"
          aria-label="Scroll to next section"
        >
          <span className="luxury-caption text-white/70 group-hover:text-luxury-gold transition-colors">
            Discover More
          </span>
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center group-hover:border-luxury-gold transition-colors">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-bounce group-hover:bg-luxury-gold transition-colors" />
          </div>
          <ChevronDown className="w-5 h-5 text-white/50 animate-bounce group-hover:text-luxury-gold transition-colors" />
        </button>
      </div>

      {/* Luxury Accent Elements */}
      <div className="absolute top-1/4 left-8 w-1 h-24 bg-gradient-to-b from-luxury-gold to-transparent opacity-60" />
      <div className="absolute bottom-1/4 right-8 w-1 h-24 bg-gradient-to-t from-luxury-gold to-transparent opacity-60" />
      
      {/* Corner Decorative Elements */}
      <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-luxury-gold/20 to-transparent" />
      <div className="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-luxury-gold/20 to-transparent" />
    </section>
  );
};

export default HeroSection;