'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Dumbbell, 
  Waves, 
  Car, 
  Shield, 
  Utensils, 
  Leaf,
  Wifi,
  Camera,
  Wind,
  Home,
  Trees,
  Building,
  Users,
  Heart,
  Baby,
  BookOpen,
  Music,
  Gamepad2,
  Coffee,
  ShoppingBag,
  Plane,
  Star,
  CheckCircle,
  Sparkles
} from 'lucide-react';
import { luxuryAnimations, initGSAP } from '@/lib/animations/gsap';
import { cn } from '@/lib/utils/cn';

interface ServiceProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  features: string[];
  category: 'wellness' | 'lifestyle' | 'security' | 'convenience' | 'recreation' | 'family';
  premium?: boolean;
}

interface ServiceCategoryProps {
  title: string;
  services: ServiceProps[];
  delay?: number;
}

const ServiceCard: React.FC<{ service: ServiceProps }> = ({ service }) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    const node = cardRef.current;
    if (!node) return;

    const hoverAnimation = luxuryAnimations.luxuryHover(node);
    const handleMouseEnter = () => hoverAnimation.play();
    const handleMouseLeave = () => hoverAnimation.reverse();

    node.addEventListener('mouseenter', handleMouseEnter);
    node.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      node.removeEventListener('mouseenter', handleMouseEnter);
      node.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  const categoryColors = {
    wellness: 'from-green-500 to-emerald-600',
    lifestyle: 'from-luxury-gold to-luxury-gold-light',
    security: 'from-blue-500 to-indigo-600',
    convenience: 'from-purple-500 to-violet-600',
    recreation: 'from-orange-500 to-red-500',
    family: 'from-pink-500 to-rose-600'
  };

  return (
    <Card 
      ref={cardRef}
      className={cn(
        "luxury-glass backdrop-blur-sm p-6 border border-luxury-gold/20 hover:border-luxury-gold/40",
        "transition-all duration-500 cursor-pointer relative overflow-hidden",
        isExpanded ? "row-span-2" : ""
      )}
      onClick={() => setIsExpanded(!isExpanded)}
    >
      {/* Premium Badge */}
      {service.premium && (
        <div className="absolute top-2 right-2">
          <div className="w-8 h-8 bg-gradient-to-br from-luxury-gold to-luxury-gold-light rounded-full flex items-center justify-center">
            <Star className="w-4 h-4 text-white" />
          </div>
        </div>
      )}

      {/* Service Icon & Title */}
      <div className="flex items-start gap-4 mb-4">
        <div className={cn(
          "w-12 h-12 rounded-xl bg-gradient-to-br flex items-center justify-center flex-shrink-0",
          categoryColors[service.category]
        )}>
          {service.icon}
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="luxury-subheading text-lg font-semibold text-white mb-2">
            {service.title}
          </h3>
          <p className={cn(
            "luxury-body text-white/70 text-sm leading-relaxed transition-all duration-300",
            isExpanded ? "line-clamp-none" : "line-clamp-2"
          )}>
            {service.description}
          </p>
        </div>
      </div>

      {/* Features List */}
      <div className={cn(
        "transition-all duration-500 overflow-hidden",
        isExpanded ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
      )}>
        <div className="pt-4 border-t border-luxury-gold/20">
          <h4 className="luxury-caption text-luxury-gold font-medium mb-3">Features:</h4>
          <ul className="space-y-2">
            {service.features.map((feature, idx) => (
              <li key={idx} className="flex items-center gap-2">
                <CheckCircle className="w-3 h-3 text-luxury-gold flex-shrink-0" />
                <span className="luxury-caption text-white/80 text-sm">{feature}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Expand Indicator */}
      <div className="flex justify-center mt-4">
        <div className={cn(
          "w-6 h-1 bg-luxury-gold/30 rounded-full transition-all duration-300",
          isExpanded ? "bg-luxury-gold" : ""
        )} />
      </div>
    </Card>
  );
};

const ServiceCategory: React.FC<ServiceCategoryProps> = ({ title, services, delay = 0 }) => {
  const categoryRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (categoryRef.current) {
      luxuryAnimations.fadeInUp(categoryRef.current, delay);
    }
  }, [delay]);

  return (
    <div ref={categoryRef} className="mb-16">
      <h3 className="luxury-subheading text-2xl font-semibold text-white mb-8 text-center">
        {title}
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {services.map((service, index) => (
          <ServiceCard key={index} service={service} />
        ))}
      </div>
    </div>
  );
};

const ServicesSection: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    initGSAP();

    if (headerRef.current) {
      luxuryAnimations.fadeInUp(headerRef.current, 0.2);
    }

    if (statsRef.current) {
      luxuryAnimations.staggerFadeIn(statsRef.current.children, 0.1);
    }
  }, []);

  const wellnessServices: ServiceProps[] = [
    {
      icon: <Dumbbell className="w-6 h-6 text-white" />,
      title: "State-of-the-Art Gymnasium",
      description: "Premium fitness facility with latest equipment, personal trainers, and wellness programs designed for luxury living.",
      features: [
        "Latest Technogym equipment",
        "Personal training sessions",
        "Yoga and meditation studio",
        "Wellness consultation",
        "Nutrition guidance programs"
      ],
      category: "wellness",
      premium: true
    },
    {
      icon: <Waves className="w-6 h-6 text-white" />,
      title: "Infinity Pool & Spa",
      description: "Resort-style infinity pool with spa facilities, steam rooms, and relaxation zones for ultimate wellness experience.",
      features: [
        "Temperature-controlled infinity pool",
        "Spa and wellness center",
        "Steam and sauna rooms",
        "Poolside cabanas",
        "Professional massage services"
      ],
      category: "wellness",
      premium: true
    },
    {
      icon: <Heart className="w-6 h-6 text-white" />,
      title: "Wellness Center",
      description: "Comprehensive health and wellness center with medical facilities and preventive healthcare programs.",
      features: [
        "On-call medical assistance",
        "Health monitoring systems",
        "Preventive health checkups",
        "Emergency medical response",
        "Telemedicine facilities"
      ],
      category: "wellness"
    }
  ];

  const lifestyleServices: ServiceProps[] = [
    {
      icon: <Utensils className="w-6 h-6 text-white" />,
      title: "Fine Dining Restaurant",
      description: "Premium dining experience with multi-cuisine restaurant, private dining rooms, and gourmet food services.",
      features: [
        "Multi-cuisine fine dining",
        "Private dining rooms",
        "Chef-curated menus",
        "Room service available",
        "Special occasion catering"
      ],
      category: "lifestyle",
      premium: true
    },
    {
      icon: <Coffee className="w-6 h-6 text-white" />,
      title: "Sky Lounge & Bar",
      description: "Exclusive rooftop lounge with panoramic city views, premium bar service, and social gathering spaces.",
      features: [
        "360-degree city views",
        "Premium beverage selection",
        "Live entertainment",
        "Private event hosting",
        "Business meeting facilities"
      ],
      category: "lifestyle",
      premium: true
    },
    {
      icon: <ShoppingBag className="w-6 h-6 text-white" />,
      title: "Luxury Concierge",
      description: "24/7 concierge services for shopping, reservations, travel planning, and lifestyle management.",
      features: [
        "Personal shopping assistance",
        "Travel planning and booking",
        "Restaurant reservations",
        "Event ticket bookings",
        "Lifestyle management services"
      ],
      category: "lifestyle"
    }
  ];

  const securityServices: ServiceProps[] = [
    {
      icon: <Shield className="w-6 h-6 text-white" />,
      title: "Advanced Security System",
      description: "Multi-layered security with 24/7 monitoring, access control, and emergency response systems.",
      features: [
        "24/7 security personnel",
        "CCTV surveillance",
        "Access control systems",
        "Visitor management",
        "Emergency response team"
      ],
      category: "security"
    },
    {
      icon: <Camera className="w-6 h-6 text-white" />,
      title: "Smart Home Integration",
      description: "Intelligent home automation with security integration, remote monitoring, and smart controls.",
      features: [
        "Smart lock systems",
        "Home automation controls",
        "Remote monitoring app",
        "Motion sensors",
        "Smart lighting controls"
      ],
      category: "security"
    }
  ];

  const convenienceServices: ServiceProps[] = [
    {
      icon: <Car className="w-6 h-6 text-white" />,
      title: "Premium Parking",
      description: "Multi-level secure parking with valet services, EV charging stations, and car wash facilities.",
      features: [
        "Reserved parking spaces",
        "Valet parking service",
        "EV charging stations",
        "Car wash facilities",
        "24/7 parking security"
      ],
      category: "convenience"
    },
    {
      icon: <Wifi className="w-6 h-6 text-white" />,
      title: "High-Speed Connectivity",
      description: "Fiber-optic internet, smart building infrastructure, and comprehensive digital services.",
      features: [
        "Gigabit fiber internet",
        "Smart building WiFi",
        "Digital service integration",
        "Video conferencing facilities",
        "Smart TV and entertainment"
      ],
      category: "convenience"
    },
    {
      icon: <Wind className="w-6 h-6 text-white" />,
      title: "Climate Control",
      description: "Advanced HVAC systems with individual climate control and air purification technology.",
      features: [
        "Individual climate control",
        "Air purification systems",
        "Energy-efficient cooling",
        "Smart temperature management",
        "Humidity control"
      ],
      category: "convenience"
    }
  ];

  const recreationServices: ServiceProps[] = [
    {
      icon: <Gamepad2 className="w-6 h-6 text-white" />,
      title: "Recreation Center",
      description: "Multi-purpose recreation facility with gaming zones, entertainment areas, and social spaces.",
      features: [
        "Gaming and entertainment zone",
        "Billiards and table tennis",
        "Movie screening room",
        "Social gathering spaces",
        "Board games and activities"
      ],
      category: "recreation"
    },
    {
      icon: <Music className="w-6 h-6 text-white" />,
      title: "Amphitheater",
      description: "Outdoor entertainment venue for community events, performances, and social gatherings.",
      features: [
        "Outdoor performance stage",
        "Community event hosting",
        "Live entertainment programs",
        "Cultural celebrations",
        "Social community events"
      ],
      category: "recreation"
    },
    {
      icon: <Trees className="w-6 h-6 text-white" />,
      title: "Landscaped Gardens",
      description: "Beautifully designed gardens with walking paths, meditation areas, and outdoor relaxation spaces.",
      features: [
        "Professional landscaping",
        "Walking and jogging paths",
        "Meditation gardens",
        "Outdoor seating areas",
        "Seasonal flower displays"
      ],
      category: "recreation"
    }
  ];

  const familyServices: ServiceProps[] = [
    {
      icon: <Baby className="w-6 h-6 text-white" />,
      title: "Kids Play Area",
      description: "Safe and engaging play facilities designed for children of all ages with supervised activities.",
      features: [
        "Age-appropriate play equipment",
        "Supervised play activities",
        "Indoor and outdoor areas",
        "Safety-certified facilities",
        "Educational play programs"
      ],
      category: "family"
    },
    {
      icon: <BookOpen className="w-6 h-6 text-white" />,
      title: "Library & Study",
      description: "Quiet study spaces with extensive book collection, digital resources, and learning facilities.",
      features: [
        "Extensive book collection",
        "Digital library access",
        "Study rooms and workspaces",
        "High-speed internet",
        "Printing and scanning facilities"
      ],
      category: "family"
    }
  ];

  return (
    <section 
      ref={sectionRef}
      id="services"
      className="relative py-20 lg:py-32 bg-luxury-charcoal-dark overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-luxury-charcoal-dark via-luxury-charcoal to-luxury-charcoal-light" />
      <div className="absolute top-0 right-0 w-full h-full opacity-5 bg-[url('https://images.pexels.com/photos/261102/pexels-photo-261102.jpeg?auto=compress&cs=tinysrgb&w=1920')] bg-cover bg-center" />
      
      {/* Decorative Elements */}
      <div className="absolute top-20 left-10 w-2 h-32 bg-gradient-to-b from-luxury-gold to-transparent opacity-60" />
      <div className="absolute bottom-20 right-10 w-2 h-32 bg-gradient-to-t from-luxury-gold to-transparent opacity-60" />

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div ref={headerRef} className="text-center mb-16">
          <div className="inline-flex items-center gap-2 luxury-glass rounded-full px-6 py-3 mb-6 backdrop-blur-sm">
            <Sparkles className="w-4 h-4 text-luxury-gold" />
            <span className="luxury-caption text-white">Premium Amenities</span>
          </div>
          
          <h2 className="luxury-heading text-4xl md:text-6xl font-bold text-white mb-6">
            Luxury{' '}
            <span className="luxury-gradient-text">Amenities</span>
            {' '}& Services
          </h2>
          
          <p className="luxury-subheading text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Experience unparalleled luxury with over 120+ premium amenities designed for the most discerning residents
          </p>
        </div>

        {/* Service Statistics */}
        <div ref={statsRef} className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20">
          <Card className="luxury-glass backdrop-blur-sm p-6 text-center border border-luxury-gold/20">
            <Building className="w-8 h-8 text-luxury-gold mx-auto mb-3" />
            <div className="luxury-heading text-2xl font-bold text-white mb-1">120+</div>
            <div className="luxury-caption text-white/70">Premium Amenities</div>
          </Card>
          
          <Card className="luxury-glass backdrop-blur-sm p-6 text-center border border-luxury-gold/20">
            <Users className="w-8 h-8 text-luxury-gold mx-auto mb-3" />
            <div className="luxury-heading text-2xl font-bold text-white mb-1">24/7</div>
            <div className="luxury-caption text-white/70">Concierge Service</div>
          </Card>
          
          <Card className="luxury-glass backdrop-blur-sm p-6 text-center border border-luxury-gold/20">
            <Star className="w-8 h-8 text-luxury-gold mx-auto mb-3" />
            <div className="luxury-heading text-2xl font-bold text-white mb-1">5-Star</div>
            <div className="luxury-caption text-white/70">Hotel Experience</div>
          </Card>
          
          <Card className="luxury-glass backdrop-blur-sm p-6 text-center border border-luxury-gold/20">
            <Leaf className="w-8 h-8 text-luxury-gold mx-auto mb-3" />
            <div className="luxury-heading text-2xl font-bold text-white mb-1">100%</div>
            <div className="luxury-caption text-white/70">Eco-Friendly</div>
          </Card>
        </div>

        {/* Service Categories */}
        <div className="space-y-20">
          <ServiceCategory 
            title="Wellness & Fitness" 
            services={wellnessServices} 
            delay={0.2}
          />
          
          <ServiceCategory 
            title="Lifestyle & Dining" 
            services={lifestyleServices} 
            delay={0.4}
          />
          
          <ServiceCategory 
            title="Security & Technology" 
            services={securityServices} 
            delay={0.6}
          />
          
          <ServiceCategory 
            title="Convenience & Comfort" 
            services={convenienceServices} 
            delay={0.8}
          />
          
          <ServiceCategory 
            title="Recreation & Entertainment" 
            services={recreationServices} 
            delay={1.0}
          />
          
          <ServiceCategory 
            title="Family & Community" 
            services={familyServices} 
            delay={1.2}
          />
        </div>

        {/* Call to Action */}
        <div className="text-center mt-20">
          <div className="luxury-glass rounded-2xl p-8 backdrop-blur-sm border border-luxury-gold/20 max-w-4xl mx-auto">
            <h3 className="luxury-subheading text-2xl font-semibold text-white mb-4">
              Experience The Sixteen Lifestyle
            </h3>
            <p className="luxury-body text-white/80 mb-6">
              Schedule a personal tour to experience our world-class amenities and luxury services firsthand.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="luxury" size="xl" className="luxury-shine-enhanced">
                <Home className="w-5 h-5" />
                Schedule Amenity Tour
              </Button>
              <Button variant="luxuryOutline" size="xl">
                <Plane className="w-5 h-5" />
                Download Brochure
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;