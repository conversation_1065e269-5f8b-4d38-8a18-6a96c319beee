// Mock property data for The Sixteen luxury real estate website
import { Property, PropertyImage, Agent } from './types';

// Sample luxury agents
const agents: Agent[] = [
  {
    id: 'agent-1',
    name: '<PERSON><PERSON>',
    title: 'Senior Luxury Property Consultant',
    email: '<EMAIL>',
    phone: '+91 9876543210',
    image: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=400',
    bio: 'Specialist in luxury residential properties with over 10 years of experience in Rourkela\'s premium real estate market.',
    specialties: ['Luxury Condos', 'Penthouse Suites', 'Investment Properties'],
    languages: ['English', 'Hindi', 'Odia'],
    socialMedia: {
      linkedin: 'https://linkedin.com/in/priya-sharma-luxury',
      instagram: 'https://instagram.com/priya_luxury_homes'
    }
  },
  {
    id: 'agent-2',
    name: '<PERSON><PERSON>',
    title: 'Premium Properties Director',
    email: 'r<PERSON><PERSON>.<EMAIL>',
    phone: '+91 9876543211',
    image: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=400',
    bio: 'Expert in commercial and high-end residential properties, serving Rourkela\'s elite clientele for over 15 years.',
    specialties: ['Commercial Spaces', 'Premium Apartments', 'Luxury Investments'],
    languages: ['English', 'Hindi', 'Bengali'],
    socialMedia: {
      linkedin: 'https://linkedin.com/in/rajesh-kumar-realty'
    }
  }
];

// Sample property images
const createPropertyImages = (propertyId: string, baseImages: string[]): PropertyImage[] => {
  return baseImages.map((image, index) => ({
    id: `${propertyId}-img-${index + 1}`,
    url: image,
    alt: `Property ${propertyId} - Image ${index + 1}`,
    caption: index === 0 ? 'Main facade' : `Interior view ${index}`,
    type: index === 0 ? 'exterior' as const : 'interior' as const,
    featured: index === 0,
    order: index + 1
  }));
};

// Luxury property data for The Sixteen
export const luxuryProperties: Property[] = [
  {
    id: 'penthouse-royal-suite',
    title: 'Royal Penthouse Suite',
    description: 'An exquisite penthouse offering panoramic views of Rourkela skyline. This crown jewel features floor-to-ceiling windows, Italian marble flooring, and bespoke luxury finishes throughout.',
    price: 45000000, // 4.5 Crores
    address: {
      street: 'The Sixteen, Sector 19',
      city: 'Rourkela',
      state: 'Odisha',
      zipCode: '769005',
      country: 'India'
    },
    specifications: {
      bedrooms: 4,
      bathrooms: 5,
      squareFeet: 3500,
      yearBuilt: 2024,
      propertyType: 'penthouse'
    },
    features: [
      'Private Elevator Access',
      'Rooftop Terrace Garden',
      'Italian Marble Flooring',
      'Smart Home Automation',
      'Wine Cellar',
      'Master Suite with Walk-in Closet',
      'Panoramic City Views',
      'Private Jacuzzi'
    ],
    amenities: [
      'Concierge Service',
      'Valet Parking',
      'Private Gym',
      'Infinity Pool',
      'Spa & Wellness Center',
      'Business Lounge',
      'Rooftop Restaurant',
      '24/7 Security'
    ],
    images: createPropertyImages('penthouse-royal-suite', [
      'https://images.pexels.com/photos/1370704/pexels-photo-1370704.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1454806/pexels-photo-1454806.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/2724749/pexels-photo-2724749.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800'
    ]),
    agent: agents[0]!,
    status: 'available',
    featured: true,
    luxury: true,
    coordinates: {
      lat: 22.2604,
      lng: 84.8536
    },
    virtualTour: 'https://virtual-tour.thesixteen.com/penthouse-royal',
    video: '/videos/properties/penthouse-royal-tour.mp4',
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-08-01T00:00:00Z'
  },
  {
    id: 'penthouse-platinum-suite',
    title: 'Platinum Penthouse Suite',
    description: 'Sophisticated penthouse featuring contemporary design and premium amenities. Perfect for those who appreciate modern luxury with traditional elegance.',
    price: 38000000, // 3.8 Crores
    address: {
      street: 'The Sixteen, Sector 19',
      city: 'Rourkela',
      state: 'Odisha',
      zipCode: '769005',
      country: 'India'
    },
    specifications: {
      bedrooms: 3,
      bathrooms: 4,
      squareFeet: 2800,
      yearBuilt: 2024,
      propertyType: 'penthouse'
    },
    features: [
      'Private Lift Lobby',
      'Premium Hardwood Flooring',
      'Designer Kitchen',
      'Master Balcony',
      'Study Room',
      'Guest Powder Room',
      'Premium Fixtures',
      'Central Air Conditioning'
    ],
    amenities: [
      'Swimming Pool',
      'Fitness Center',
      '24/7 Security',
      'Power Backup',
      'Elevator Access',
      'Parking Space',
      'Garden Area',
      'Maintenance Service'
    ],
    images: createPropertyImages('penthouse-platinum-suite', [
      'https://images.pexels.com/photos/323705/pexels-photo-323705.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/2119714/pexels-photo-2119714.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/271816/pexels-photo-271816.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/2724748/pexels-photo-2724748.jpeg?auto=compress&cs=tinysrgb&w=800'
    ]),
    agent: agents[1]!,
    status: 'available',
    featured: true,
    luxury: true,
    coordinates: {
      lat: 22.2604,
      lng: 84.8536
    },
    createdAt: '2024-01-20T00:00:00Z',
    updatedAt: '2024-07-28T00:00:00Z'
  },
  {
    id: 'luxury-apartment-gold',
    title: 'Gold Luxury Apartment',
    description: 'Elegantly designed luxury apartment with premium finishes and modern amenities. Ideal for executive living with style and comfort.',
    price: 28000000, // 2.8 Crores
    address: {
      street: 'The Sixteen, Sector 19',
      city: 'Rourkela',
      state: 'Odisha',
      zipCode: '769005',
      country: 'India'
    },
    specifications: {
      bedrooms: 3,
      bathrooms: 3,
      squareFeet: 2200,
      yearBuilt: 2024,
      propertyType: 'condo'
    },
    features: [
      'Modular Kitchen',
      'Marble Countertops',
      'Walk-in Wardrobe',
      'Balcony Garden',
      'Premium Lighting',
      'Wooden Flooring',
      'Built-in Storage',
      'Utility Area'
    ],
    amenities: [
      'Club House',
      'Swimming Pool',
      'Gym & Spa',
      'Children\'s Play Area',
      'Landscaped Gardens',
      'Jogging Track',
      'Multi-purpose Hall',
      'Visitor Parking'
    ],
    images: createPropertyImages('luxury-apartment-gold', [
      'https://images.pexels.com/photos/280229/pexels-photo-280229.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1571453/pexels-photo-1571453.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1454805/pexels-photo-1454805.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/2724748/pexels-photo-2724748.jpeg?auto=compress&cs=tinysrgb&w=800'
    ]),
    agent: agents[0]!,
    status: 'available',
    featured: false,
    luxury: true,
    coordinates: {
      lat: 22.2604,
      lng: 84.8536
    },
    createdAt: '2024-02-01T00:00:00Z',
    updatedAt: '2024-07-25T00:00:00Z'
  },
  {
    id: 'premium-apartment-silver',
    title: 'Silver Premium Apartment',
    description: 'Stylish premium apartment featuring contemporary design and quality finishes. Perfect for modern family living with luxury touches.',
    price: 22000000, // 2.2 Crores
    address: {
      street: 'The Sixteen, Sector 19',
      city: 'Rourkela',
      state: 'Odisha',
      zipCode: '769005',
      country: 'India'
    },
    specifications: {
      bedrooms: 2,
      bathrooms: 2,
      squareFeet: 1800,
      yearBuilt: 2024,
      propertyType: 'condo'
    },
    features: [
      'Open Plan Living',
      'Modern Kitchen',
      'Large Windows',
      'Storage Solutions',
      'Quality Finishes',
      'Balcony Access',
      'Ensuite Bathroom',
      'Guest Toilet'
    ],
    amenities: [
      'Swimming Pool',
      'Fitness Center',
      'Community Hall',
      'Children\'s Area',
      'Security System',
      'Elevator',
      'Parking',
      'Garden Space'
    ],
    images: createPropertyImages('premium-apartment-silver', [
      'https://images.pexels.com/photos/259588/pexels-photo-259588.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1571468/pexels-photo-1571468.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1454804/pexels-photo-1454804.jpeg?auto=compress&cs=tinysrgb&w=800'
    ]),
    agent: agents[1]!,
    status: 'available',
    featured: false,
    luxury: true,
    coordinates: {
      lat: 22.2604,
      lng: 84.8536
    },
    createdAt: '2024-02-10T00:00:00Z',
    updatedAt: '2024-07-20T00:00:00Z'
  },
  {
    id: 'commercial-office-suite',
    title: 'Executive Office Suite',
    description: 'Premium commercial space designed for modern businesses. Features state-of-the-art facilities and stunning city views.',
    price: 35000000, // 3.5 Crores
    address: {
      street: 'The Sixteen, Sector 19',
      city: 'Rourkela',
      state: 'Odisha',
      zipCode: '769005',
      country: 'India'
    },
    specifications: {
      bedrooms: 0,
      bathrooms: 4,
      squareFeet: 2500,
      yearBuilt: 2024,
      propertyType: 'condo'
    },
    features: [
      'Open Office Space',
      'Conference Rooms',
      'Executive Cabins',
      'Reception Area',
      'Pantry Facility',
      'Server Room',
      'Storage Space',
      'High-speed Internet'
    ],
    amenities: [
      'Business Lounge',
      'Meeting Rooms',
      'Cafeteria',
      'Parking Facility',
      '24/7 Access',
      'Security System',
      'Maintenance',
      'Reception Services'
    ],
    images: createPropertyImages('commercial-office-suite', [
      'https://images.pexels.com/photos/2467285/pexels-photo-2467285.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1170412/pexels-photo-1170412.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1181396/pexels-photo-1181396.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1181354/pexels-photo-1181354.jpeg?auto=compress&cs=tinysrgb&w=800'
    ]),
    agent: agents[1]!,
    status: 'available',
    featured: false,
    luxury: false,
    coordinates: {
      lat: 22.2604,
      lng: 84.8536
    },
    createdAt: '2024-02-15T00:00:00Z',
    updatedAt: '2024-07-18T00:00:00Z'
  },
  {
    id: 'luxury-apartment-diamond',
    title: 'Diamond Luxury Residence',
    description: 'Exceptional luxury apartment with designer interiors and premium amenities. A perfect blend of comfort and sophistication.',
    price: 32000000, // 3.2 Crores
    address: {
      street: 'The Sixteen, Sector 19',
      city: 'Rourkela',
      state: 'Odisha',
      zipCode: '769005',
      country: 'India'
    },
    specifications: {
      bedrooms: 3,
      bathrooms: 3,
      squareFeet: 2400,
      yearBuilt: 2024,
      propertyType: 'condo'
    },
    features: [
      'Designer Interiors',
      'Premium Appliances',
      'Walk-in Closets',
      'Private Balcony',
      'Luxury Bathrooms',
      'Imported Fixtures',
      'Hardwood Floors',
      'Smart Home Features'
    ],
    amenities: [
      'Infinity Pool',
      'Spa & Wellness',
      'Private Gym',
      'Concierge Service',
      'Valet Parking',
      'Rooftop Lounge',
      'Business Center',
      'Guest Suites'
    ],
    images: createPropertyImages('luxury-apartment-diamond', [
      'https://images.pexels.com/photos/1370704/pexels-photo-1370704.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/2119714/pexels-photo-2119714.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/271816/pexels-photo-271816.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1454806/pexels-photo-1454806.jpeg?auto=compress&cs=tinysrgb&w=800'
    ]),
    agent: agents[0]!,
    status: 'available',
    featured: true,
    luxury: true,
    coordinates: {
      lat: 22.2604,
      lng: 84.8536
    },
    virtualTour: 'https://virtual-tour.thesixteen.com/diamond-residence',
    createdAt: '2024-02-20T00:00:00Z',
    updatedAt: '2024-07-15T00:00:00Z'
  },
  {
    id: 'premium-studio-loft',
    title: 'Premium Studio Loft',
    description: 'Modern studio loft perfect for young professionals. Features contemporary design with all essential amenities.',
    price: 18000000, // 1.8 Crores
    address: {
      street: 'The Sixteen, Sector 19',
      city: 'Rourkela',
      state: 'Odisha',
      zipCode: '769005',
      country: 'India'
    },
    specifications: {
      bedrooms: 1,
      bathrooms: 1,
      squareFeet: 1200,
      yearBuilt: 2024,
      propertyType: 'loft'
    },
    features: [
      'Open Floor Plan',
      'Loft-style Ceiling',
      'Modern Kitchen',
      'Large Windows',
      'Built-in Storage',
      'Hardwood Floors',
      'Balcony Space',
      'Quality Finishes'
    ],
    amenities: [
      'Fitness Center',
      'Swimming Pool',
      'Community Space',
      'Co-working Area',
      'Rooftop Garden',
      'Parking Space',
      'Security',
      'Maintenance'
    ],
    images: createPropertyImages('premium-studio-loft', [
      'https://images.pexels.com/photos/280229/pexels-photo-280229.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1571463/pexels-photo-1571463.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/2724748/pexels-photo-2724748.jpeg?auto=compress&cs=tinysrgb&w=800'
    ]),
    agent: agents[0]!,
    status: 'available',
    featured: false,
    luxury: false,
    coordinates: {
      lat: 22.2604,
      lng: 84.8536
    },
    createdAt: '2024-03-01T00:00:00Z',
    updatedAt: '2024-07-10T00:00:00Z'
  },
  {
    id: 'commercial-retail-space',
    title: 'Premium Retail Space',
    description: 'Strategic retail space in the heart of Rourkela\'s premium district. Ideal for luxury brands and high-end retail businesses.',
    price: 25000000, // 2.5 Crores
    address: {
      street: 'The Sixteen, Sector 19',
      city: 'Rourkela',
      state: 'Odisha',
      zipCode: '769005',
      country: 'India'
    },
    specifications: {
      bedrooms: 0,
      bathrooms: 2,
      squareFeet: 1800,
      yearBuilt: 2024,
      propertyType: 'condo'
    },
    features: [
      'Street Front Access',
      'Large Display Windows',
      'High Ceilings',
      'Storage Area',
      'Loading Dock',
      'Customer Parking',
      'Modern Lighting',
      'Security System'
    ],
    amenities: [
      'Mall Entrance',
      'Food Court Access',
      'Customer Parking',
      'Security Services',
      'Maintenance',
      'Common Areas',
      'Elevator Access',
      'Emergency Systems'
    ],
    images: createPropertyImages('commercial-retail-space', [
      'https://images.pexels.com/photos/2467285/pexels-photo-2467285.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1170412/pexels-photo-1170412.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1181396/pexels-photo-1181396.jpeg?auto=compress&cs=tinysrgb&w=800'
    ]),
    agent: agents[1]!,
    status: 'available',
    featured: false,
    luxury: false,
    coordinates: {
      lat: 22.2604,
      lng: 84.8536
    },
    createdAt: '2024-03-05T00:00:00Z',
    updatedAt: '2024-07-08T00:00:00Z'
  },
  {
    id: 'penthouse-emerald-suite',
    title: 'Emerald Penthouse Collection',
    description: 'Ultra-luxurious penthouse with breathtaking views and world-class amenities. The epitome of luxury living in Rourkela.',
    price: 52000000, // 5.2 Crores
    address: {
      street: 'The Sixteen, Sector 19',
      city: 'Rourkela',
      state: 'Odisha',
      zipCode: '769005',
      country: 'India'
    },
    specifications: {
      bedrooms: 5,
      bathrooms: 6,
      squareFeet: 4200,
      yearBuilt: 2024,
      propertyType: 'penthouse'
    },
    features: [
      'Private Elevator',
      'Wraparound Terrace',
      'Home Theater',
      'Wine Cellar',
      'Master Suite Wing',
      'Guest Quarters',
      'Private Office',
      'Luxury Spa Bathroom',
      'Chef\'s Kitchen',
      'Fire Place'
    ],
    amenities: [
      'Concierge Service',
      'Private Valet',
      'Helicopter Pad Access',
      'Private Dining',
      'Exclusive Pool',
      'Personal Trainer',
      'Spa Services',
      'Butler Service'
    ],
    images: createPropertyImages('penthouse-emerald-suite', [
      'https://images.pexels.com/photos/323705/pexels-photo-323705.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/271816/pexels-photo-271816.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/2724749/pexels-photo-2724749.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1571453/pexels-photo-1571453.jpeg?auto=compress&cs=tinysrgb&w=800'
    ]),
    agent: agents[0]!,
    status: 'available',
    featured: true,
    luxury: true,
    coordinates: {
      lat: 22.2604,
      lng: 84.8536
    },
    virtualTour: 'https://virtual-tour.thesixteen.com/emerald-penthouse',
    video: '/videos/properties/emerald-penthouse-tour.mp4',
    createdAt: '2024-03-10T00:00:00Z',
    updatedAt: '2024-08-05T00:00:00Z'
  },
  {
    id: 'luxury-apartment-sapphire',
    title: 'Sapphire Luxury Collection',
    description: 'Sophisticated luxury apartment with premium finishes and stunning architectural details. Perfect for discerning buyers.',
    price: 35000000, // 3.5 Crores
    address: {
      street: 'The Sixteen, Sector 19',
      city: 'Rourkela',
      state: 'Odisha',
      zipCode: '769005',
      country: 'India'
    },
    specifications: {
      bedrooms: 4,
      bathrooms: 4,
      squareFeet: 2800,
      yearBuilt: 2024,
      propertyType: 'condo'
    },
    features: [
      'Cathedral Ceilings',
      'Floor-to-ceiling Windows',
      'Italian Marble',
      'Custom Millwork',
      'Designer Kitchen',
      'Master Suite',
      'Guest Wing',
      'Private Study',
      'Luxury Fixtures',
      'Smart Automation'
    ],
    amenities: [
      'Infinity Pool',
      'Private Gym',
      'Spa & Wellness',
      'Business Lounge',
      'Wine Storage',
      'Concierge',
      'Valet Service',
      'Private Events'
    ],
    images: createPropertyImages('luxury-apartment-sapphire', [
      'https://images.pexels.com/photos/259588/pexels-photo-259588.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/2119714/pexels-photo-2119714.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1571468/pexels-photo-1571468.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1454805/pexels-photo-1454805.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/2724748/pexels-photo-2724748.jpeg?auto=compress&cs=tinysrgb&w=800'
    ]),
    agent: agents[1]!,
    status: 'pending',
    featured: true,
    luxury: true,
    coordinates: {
      lat: 22.2604,
      lng: 84.8536
    },
    virtualTour: 'https://virtual-tour.thesixteen.com/sapphire-collection',
    createdAt: '2024-03-15T00:00:00Z',
    updatedAt: '2024-08-03T00:00:00Z'
  }
];

// Filter properties by type
export const getPropertiesByType = (type: string) => {
  if (type === 'all') return luxuryProperties;
  
  switch (type) {
    case 'penthouse':
      return luxuryProperties.filter(p => p.specifications.propertyType === 'penthouse');
    case 'luxury':
      return luxuryProperties.filter(p => p.luxury && p.specifications.propertyType === 'condo');
    case 'premium':
      return luxuryProperties.filter(p => !p.luxury && p.specifications.propertyType !== 'penthouse');
    case 'commercial':
      return luxuryProperties.filter(p => p.specifications.bedrooms === 0);
    default:
      return luxuryProperties;
  }
};

// Get featured properties
export const getFeaturedProperties = () => {
  return luxuryProperties.filter(p => p.featured);
};

// Format price in Indian currency
export const formatPrice = (price: number): string => {
  if (price >= 10000000) {
    return `₹${(price / 10000000).toFixed(1)} Cr`;
  } else if (price >= 100000) {
    return `₹${(price / 100000).toFixed(1)} L`;
  } else {
    return `₹${price.toLocaleString('en-IN')}`;
  }
};

export default luxuryProperties;