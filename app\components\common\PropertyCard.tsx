'use client';

import React, { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Bed, 
  Bath, 
  Square, 
  Eye, 
  Calendar, 
  Star,
  ChevronLeft,
  ChevronRight,
  Building2,
  Sparkles
} from 'lucide-react';
import { Property } from '@/data/types';
import { formatPrice } from '@/data/properties';
import { luxuryAnimations } from '@/lib/animations/gsap';
import { cn } from '@/lib/utils/cn';

interface PropertyCardProps {
  property: Property;
  onViewDetails: (property: Property) => void;
  onScheduleTour: (property: Property) => void;
  className?: string;
  featured?: boolean;
}

const PropertyCard: React.FC<PropertyCardProps> = ({
  property,
  onViewDetails,
  onScheduleTour,
  className,
  featured = false
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const hoverAnimation = useRef<{ play: () => void; reverse: () => void } | null>(null);

  useEffect(() => {
    if (cardRef.current) {
      hoverAnimation.current = luxuryAnimations.luxuryHover(cardRef.current);
    }
  }, []);

  const handleMouseEnter = () => {
    hoverAnimation.current?.play();
  };

  const handleMouseLeave = () => {
    hoverAnimation.current?.reverse();
  };

  const nextImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex((prev) => 
      prev === property.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex((prev) => 
      prev === 0 ? property.images.length - 1 : prev - 1
    );
  };

  const getPropertyTypeLabel = (type: string) => {
    switch (type) {
      case 'penthouse':
        return 'Penthouse';
      case 'condo':
        return 'Luxury Apartment';
      case 'loft':
        return 'Premium Loft';
      default:
        return 'Property';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-500';
      case 'pending':
        return 'bg-yellow-500';
      case 'sold':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <Card
      ref={cardRef}
      className={cn(
        "group relative overflow-hidden cursor-pointer luxury-glass-enhanced border-luxury-gold/20 hover:border-luxury-gold/40 transition-all duration-500 hover:shadow-2xl hover:shadow-luxury-gold/20",
        featured && "ring-2 ring-luxury-gold/50 shadow-lg shadow-luxury-gold/10",
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={() => onViewDetails(property)}
    >
      {/* Property Image Carousel */}
      <div className="relative h-64 md:h-72 overflow-hidden">
        {/* Status Badge */}
        <div className="absolute top-4 left-4 z-10 flex gap-2">
          <Badge className={cn("text-white border-0", getStatusColor(property.status))}>
            {property.status.charAt(0).toUpperCase() + property.status.slice(1)}
          </Badge>
          {property.luxury && (
            <Badge className="bg-luxury-gold text-white border-0">
              <Sparkles className="w-3 h-3 mr-1" />
              Luxury
            </Badge>
          )}
          {property.featured && (
            <Badge className="bg-luxury-charcoal text-luxury-gold border-luxury-gold/20">
              <Star className="w-3 h-3 mr-1" />
              Featured
            </Badge>
          )}
        </div>

        {/* Property Type Badge */}
        <div className="absolute top-4 right-4 z-10">
          <Badge variant="outline" className="bg-white/90 text-luxury-charcoal border-luxury-gold/20">
            <Building2 className="w-3 h-3 mr-1" />
            {getPropertyTypeLabel(property.specifications.propertyType)}
          </Badge>
        </div>

        {/* Image */}
        <div className="relative w-full h-full">
          <Image
            src={property.images[currentImageIndex]?.url || '/images/properties/placeholder.svg'}
            alt={property.images[currentImageIndex]?.alt || property.title}
            fill
            className="object-cover transition-transform duration-700 group-hover:scale-110"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          
          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-luxury-charcoal/60 via-transparent to-transparent" />
        </div>

        {/* Navigation Arrows */}
        {property.images.length > 1 && (
          <>
            <button
              onClick={prevImage}
              className="absolute left-2 top-1/2 -translate-y-1/2 z-20 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity hover:bg-white/30"
            >
              <ChevronLeft className="w-4 h-4 text-white" />
            </button>
            <button
              onClick={nextImage}
              className="absolute right-2 top-1/2 -translate-y-1/2 z-20 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity hover:bg-white/30"
            >
              <ChevronRight className="w-4 h-4 text-white" />
            </button>
          </>
        )}

        {/* Image Indicators */}
        {property.images.length > 1 && (
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-10 flex gap-1">
            {property.images.map((_, index) => (
              <button
                key={index}
                onClick={(e) => {
                  e.stopPropagation();
                  setCurrentImageIndex(index);
                }}
                className={cn(
                  "w-2 h-2 rounded-full transition-all",
                  index === currentImageIndex 
                    ? "bg-luxury-gold" 
                    : "bg-white/50 hover:bg-white/70"
                )}
              />
            ))}
          </div>
        )}

        {/* Virtual Tour Badge */}
        {property.virtualTour && (
          <div className="absolute bottom-4 right-4 z-10">
            <Badge className="bg-luxury-gold/90 text-white border-0 hover:bg-luxury-gold">
              <Eye className="w-3 h-3 mr-1" />
              Virtual Tour
            </Badge>
          </div>
        )}
      </div>

      {/* Property Details */}
      <div className="p-6">
        {/* Price */}
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-2xl font-bold luxury-gradient-text">
            {formatPrice(property.price)}
          </h3>
          <Badge variant="outline" className="text-xs text-luxury-charcoal">
            <Calendar className="w-3 h-3 mr-1" />
            {new Date(property.createdAt).getFullYear()}
          </Badge>
        </div>

        {/* Title */}
        <h4 className="text-xl font-semibold luxury-heading mb-2 line-clamp-1">
          {property.title}
        </h4>

        {/* Location */}
        <div className="flex items-center text-luxury-charcoal/70 mb-4">
          <MapPin className="w-4 h-4 mr-2 text-luxury-gold" />
          <span className="text-sm line-clamp-1">
            {property.address.street}, {property.address.city}
          </span>
        </div>

        {/* Property Stats */}
        <div className="flex items-center gap-4 mb-4 text-sm text-luxury-charcoal/70">
          {property.specifications.bedrooms > 0 && (
            <div className="flex items-center gap-1">
              <Bed className="w-4 h-4 text-luxury-gold" />
              <span>{property.specifications.bedrooms} Bed</span>
            </div>
          )}
          <div className="flex items-center gap-1">
            <Bath className="w-4 h-4 text-luxury-gold" />
            <span>{property.specifications.bathrooms} Bath</span>
          </div>
          <div className="flex items-center gap-1">
            <Square className="w-4 h-4 text-luxury-gold" />
            <span>{property.specifications.squareFeet.toLocaleString()} sq ft</span>
          </div>
        </div>

        {/* Features Preview */}
        <div className="mb-4">
          <p className="text-sm text-luxury-charcoal/70 line-clamp-2 leading-relaxed">
            {property.description}
          </p>
        </div>

        {/* Key Features */}
        <div className="flex flex-wrap gap-1 mb-4">
          {property.features.slice(0, 3).map((feature, index) => (
            <Badge 
              key={index} 
              variant="outline" 
              className="text-xs text-luxury-charcoal/70 border-luxury-gold/20"
            >
              {feature}
            </Badge>
          ))}
          {property.features.length > 3 && (
            <Badge 
              variant="outline" 
              className="text-xs text-luxury-gold border-luxury-gold/20"
            >
              +{property.features.length - 3} more
            </Badge>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button 
            variant="luxury" 
            size="sm" 
            className="flex-1 luxury-shine text-sm h-9"
            onClick={(e) => {
              e.stopPropagation();
              onViewDetails(property);
            }}
          >
            <Eye className="w-4 h-4 mr-1" />
            View Details
          </Button>
          <Button 
            variant="luxuryOutline" 
            size="sm" 
            className="flex-1 text-sm h-9"
            onClick={(e) => {
              e.stopPropagation();
              onScheduleTour(property);
            }}
          >
            <Calendar className="w-4 h-4 mr-1" />
            Schedule Tour
          </Button>
        </div>
      </div>

      {/* Luxury Accent */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-luxury-gold via-luxury-champagne to-luxury-gold opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      
      {/* Corner Decorative Elements */}
      <div className="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-luxury-gold/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-luxury-gold/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    </Card>
  );
};

export default PropertyCard;