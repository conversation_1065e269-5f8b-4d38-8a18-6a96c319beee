// Type definitions for luxury real estate data

export interface Property {
  id: string;
  title: string;
  description: string;
  price: number;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  specifications: {
    bedrooms: number;
    bathrooms: number;
    squareFeet: number;
    lotSize?: number;
    yearBuilt: number;
    propertyType: PropertyType;
  };
  features: string[];
  amenities: string[];
  images: PropertyImage[];
  agent: Agent;
  status: PropertyStatus;
  featured: boolean;
  luxury: boolean;
  coordinates?: {
    lat: number;
    lng: number;
  };
  virtualTour?: string;
  video?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PropertyImage {
  id: string;
  url: string;
  alt: string;
  caption?: string;
  type: 'exterior' | 'interior' | 'amenity' | 'view' | 'floorplan';
  featured: boolean;
  order: number;
}

export interface Agent {
  id: string;
  name: string;
  title: string;
  email: string;
  phone: string;
  image: string;
  bio: string;
  specialties: string[];
  languages: string[];
  socialMedia?: {
    linkedin?: string;
    instagram?: string;
    facebook?: string;
  };
}

export interface Neighborhood {
  id: string;
  name: string;
  description: string;
  image: string;
  averagePrice: number;
  propertyCount: number;
  highlights: string[];
  amenities: string[];
  schools: School[];
  transportation: TransportationOption[];
}

export interface School {
  name: string;
  type: 'elementary' | 'middle' | 'high' | 'private' | 'university';
  rating: number;
  distance: number;
}

export interface TransportationOption {
  type: 'subway' | 'bus' | 'train' | 'airport';
  name: string;
  distance: number;
  walkTime?: number;
}

export interface SearchFilters {
  minPrice?: number;
  maxPrice?: number;
  bedrooms?: number;
  bathrooms?: number;
  minSquareFeet?: number;
  maxSquareFeet?: number;
  propertyType?: PropertyType[];
  neighborhoods?: string[];
  features?: string[];
  luxury?: boolean;
  sortBy?: SortOption;
  sortOrder?: 'asc' | 'desc';
}

export interface ContactForm {
  name: string;
  email: string;
  phone?: string;
  message: string;
  propertyId?: string;
  preferredContact: 'email' | 'phone' | 'text';
  bestTimeToContact?: string;
}

export interface NewsletterSubscription {
  email: string;
  preferences: {
    newListings: boolean;
    priceChanges: boolean;
    marketUpdates: boolean;
    luxurySpotlight: boolean;
  };
}

export type PropertyType = 'penthouse' | 'mansion' | 'estate' | 'condo' | 'townhouse' | 'loft' | 'villa';
export type PropertyStatus = 'available' | 'pending' | 'sold' | 'off-market';
export type SortOption = 'price' | 'date' | 'size' | 'bedrooms' | 'featured';