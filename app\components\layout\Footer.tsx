'use client';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  Facebook, 
  Instagram, 
  Twitter, 
  Linkedin,
  ArrowRight
} from 'lucide-react';

interface FooterProps {
  className?: string;
}

const navigationLinks = [
  { label: 'Home', href: '#home' },
  { label: 'Properties', href: '#properties' },
  { label: 'About Us', href: '#about' },
  { label: 'Gallery', href: '#gallery' },
  { label: 'Location', href: '#location' },
  { label: 'Contact', href: '#contact' },
];

const propertyTypes = [
  { label: 'Luxury Apartments', href: '#luxury-apartments' },
  { label: 'Penthouses', href: '#penthouses' },
  { label: 'Premium Studios', href: '#premium-studios' },
  { label: 'Executive Suites', href: '#executive-suites' },
  { label: 'Garden Residences', href: '#garden-residences' },
  { label: 'Sky Villas', href: '#sky-villas' },
];

const socialLinks = [
  { icon: Facebook, href: '#', label: 'Facebook' },
  { icon: Instagram, href: '#', label: 'Instagram' },
  { icon: Twitter, href: '#', label: 'Twitter' },
  { icon: Linkedin, href: '#', label: 'LinkedIn' },
];

const legalLinks = [
  { label: 'Privacy Policy', href: '#privacy' },
  { label: 'Terms of Service', href: '#terms' },
  { label: 'Cookie Policy', href: '#cookies' },
  { label: 'Accessibility', href: '#accessibility' },
];

export function Footer({ className }: FooterProps) {
  const handleNavClick = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const currentYear = new Date().getFullYear();

  return (
    <footer className={cn('bg-luxury-charcoal text-white', className)}>
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Company Information */}
          <div className="space-y-6">
            <div className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-gradient-to-br from-luxury-gold to-luxury-gold-dark rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">16</span>
              </div>
              <div className="flex flex-col">
                <h2 className="text-2xl font-bold luxury-gradient-text">
                  The Sixteen
                </h2>
                <p className="text-xs text-luxury-platinum tracking-widest uppercase">
                  Luxury Living
                </p>
              </div>
            </div>
            <p className="text-luxury-platinum leading-relaxed">
              Where luxury meets excellence. Experience unparalleled living in the heart of the city with our premium residential offerings designed for the discerning few.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <a
                    key={social.label}
                    href={social.href}
                    aria-label={social.label}
                    className="w-10 h-10 bg-luxury-charcoal-light hover:bg-luxury-gold rounded-lg flex items-center justify-center transition-all duration-300 hover:scale-110"
                  >
                    <Icon className="h-5 w-5" />
                  </a>
                );
              })}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-luxury-gold">Quick Links</h3>
            <ul className="space-y-3">
              {navigationLinks.map((link) => (
                <li key={link.label}>
                  <button
                    onClick={() => handleNavClick(link.href)}
                    className="text-luxury-platinum hover:text-luxury-gold transition-colors duration-200 flex items-center group"
                  >
                    <ArrowRight className="h-4 w-4 mr-2 transform group-hover:translate-x-1 transition-transform" />
                    {link.label}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Property Types */}
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-luxury-gold">Property Types</h3>
            <ul className="space-y-3">
              {propertyTypes.map((property) => (
                <li key={property.label}>
                  <button
                    onClick={() => handleNavClick(property.href)}
                    className="text-luxury-platinum hover:text-luxury-gold transition-colors duration-200 flex items-center group"
                  >
                    <ArrowRight className="h-4 w-4 mr-2 transform group-hover:translate-x-1 transition-transform" />
                    {property.label}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Information */}
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-luxury-gold">Contact Info</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-luxury-gold mt-1 flex-shrink-0" />
                <div>
                  <p className="text-luxury-platinum">
                    123 Luxury Avenue<br />
                    Premium District<br />
                    New York, NY 10001
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-luxury-gold flex-shrink-0" />
                <a
                  href="tel:+1234567890"
                  className="text-luxury-platinum hover:text-luxury-gold transition-colors"
                >
                  (*************
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-luxury-gold flex-shrink-0" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-luxury-platinum hover:text-luxury-gold transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-start space-x-3">
                <Clock className="h-5 w-5 text-luxury-gold mt-1 flex-shrink-0" />
                <div className="text-luxury-platinum">
                  <p>Mon - Fri: 9:00 AM - 7:00 PM</p>
                  <p>Sat - Sun: 10:00 AM - 6:00 PM</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter Section */}
      <div className="border-t border-luxury-charcoal-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="max-w-2xl mx-auto text-center space-y-6">
            <h3 className="text-2xl font-bold text-luxury-gold">
              Stay Updated with Luxury
            </h3>
            <p className="text-luxury-platinum">
              Subscribe to our newsletter for exclusive property listings, luxury lifestyle content, and special offers.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 bg-luxury-charcoal-light border border-luxury-gold/20 rounded-lg text-white placeholder-luxury-platinum focus:outline-none focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
              />
              <Button className="luxury-gradient-gold hover:shadow-lg transition-all duration-300 luxury-shine-enhanced px-8">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-luxury-charcoal-light bg-luxury-charcoal-dark">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-luxury-platinum text-sm">
              © {currentYear} The Sixteen. All rights reserved.
            </div>
            <div className="flex flex-wrap justify-center md:justify-end space-x-6">
              {legalLinks.map((link) => (
                <button
                  key={link.label}
                  onClick={() => handleNavClick(link.href)}
                  className="text-luxury-platinum hover:text-luxury-gold transition-colors text-sm"
                >
                  {link.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}