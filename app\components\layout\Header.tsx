'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { Menu, Phone, Mail, X } from 'lucide-react';

interface HeaderProps {
  className?: string;
}

const navigationItems = [
  { label: 'Home', href: '#home' },
  { label: 'Properties', href: '#properties' },
  { label: 'About', href: '#about' },
  { label: 'Gallery', href: '#gallery' },
  { label: 'Location', href: '#location' },
  { label: 'Contact', href: '#contact' },
];

export function Header({ className }: HeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavClick = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMobileMenuOpen(false);
  };

  return (
    <header
      className={cn(
        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',
        isScrolled
          ? 'luxury-glass-enhanced shadow-lg py-3'
          : 'bg-transparent py-6',
        className
      )}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-gradient-to-br from-luxury-gold to-luxury-gold-dark rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">16</span>
            </div>
            <div className="flex flex-col">
              <h1 className="text-2xl font-bold luxury-gradient-text">
                The Sixteen
              </h1>
              <p className="text-xs text-muted-foreground tracking-widest uppercase">
                Luxury Living
              </p>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <button
                key={item.label}
                onClick={() => handleNavClick(item.href)}
                className="relative text-sm font-medium text-foreground hover:text-luxury-gold transition-colors duration-200 group"
              >
                {item.label}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-luxury-gold transition-all duration-300 group-hover:w-full" />
              </button>
            ))}
          </nav>

          {/* Contact Info & CTA */}
          <div className="hidden lg:flex items-center space-x-6">
            <div className="flex items-center space-x-4 text-sm">
              <a
                href="tel:+1234567890"
                className="flex items-center space-x-2 text-muted-foreground hover:text-luxury-gold transition-colors"
              >
                <Phone className="h-4 w-4" />
                <span>(*************</span>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-2 text-muted-foreground hover:text-luxury-gold transition-colors"
              >
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </a>
            </div>
            <Button
              onClick={() => handleNavClick('#contact')}
              className="luxury-gradient-gold hover:shadow-lg transition-all duration-300 luxury-shine-enhanced"
            >
              Schedule Tour
            </Button>
          </div>

          {/* Mobile Menu Trigger */}
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden"
                aria-label="Open navigation menu"
              >
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent
              side="right"
              className="w-full sm:w-96 bg-white/95 backdrop-blur-xl border-l border-luxury-gold/20"
            >
              <div className="flex flex-col h-full">
                {/* Mobile Logo */}
                <div className="flex items-center justify-between pb-6 border-b border-luxury-gold/10">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-luxury-gold to-luxury-gold-dark rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-lg">16</span>
                    </div>
                    <div className="flex flex-col">
                      <h1 className="text-xl font-bold luxury-gradient-text">
                        The Sixteen
                      </h1>
                      <p className="text-xs text-muted-foreground tracking-widest uppercase">
                        Luxury Living
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsMobileMenuOpen(false)}
                    aria-label="Close navigation menu"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                {/* Mobile Navigation */}
                <nav className="flex-1 py-6">
                  <ul className="space-y-4">
                    {navigationItems.map((item) => (
                      <li key={item.label}>
                        <button
                          onClick={() => handleNavClick(item.href)}
                          className="w-full text-left py-3 px-4 rounded-lg text-lg font-medium text-foreground hover:bg-luxury-champagne/50 hover:text-luxury-gold transition-all duration-200"
                        >
                          {item.label}
                        </button>
                      </li>
                    ))}
                  </ul>
                </nav>

                {/* Mobile Contact Info */}
                <div className="border-t border-luxury-gold/10 pt-6 space-y-4">
                  <div className="space-y-3">
                    <a
                      href="tel:+1234567890"
                      className="flex items-center space-x-3 text-muted-foreground hover:text-luxury-gold transition-colors"
                    >
                      <Phone className="h-5 w-5" />
                      <span>(*************</span>
                    </a>
                    <a
                      href="mailto:<EMAIL>"
                      className="flex items-center space-x-3 text-muted-foreground hover:text-luxury-gold transition-colors"
                    >
                      <Mail className="h-5 w-5" />
                      <span><EMAIL></span>
                    </a>
                  </div>
                  <Button
                    onClick={() => handleNavClick('#contact')}
                    className="w-full luxury-gradient-gold hover:shadow-lg transition-all duration-300 luxury-shine-enhanced"
                  >
                    Schedule Tour
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}