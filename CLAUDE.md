# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

"The Sixteen" is a luxury real estate website built with Next.js 15, React 19, and TypeScript. The project uses Tailwind CSS with custom luxury-themed colors, shadcn/ui components, and animation libraries (Framer Motion, GSAP) for premium user experience.

## Common Development Commands

```bash
# Development server with Turbopack
npm run dev

# Production build
npm run build

# Start production server
npm start

# Lint code
npm run lint
```

## Architecture & Structure

### App Router Structure (Next.js 15)
- Uses App Router with `app/` directory structure
- Layout defined in `app/layout.tsx` with Header/Footer
- Main page sections: Hero, Property Showcase, About

### Component Organization
```
app/components/
├── common/        # Reusable components (PropertyCard)
├── layout/        # Layout components (Header, Footer)  
├── sections/      # Page sections (HeroSection, PropertyShowcase, AboutSection)
└── ui/           # shadcn/ui components (button, card, dialog, etc.)
```

### Data Layer
- Type definitions in `app/data/types.ts` with comprehensive Property, Agent, Neighborhood interfaces
- Mock data in `app/data/mockData.ts` and `app/data/properties.ts`
- Centralized exports from `app/data/index.ts`

### Styling & Design System
- **Tailwind CSS** with custom luxury color palette in `tailwind.config.js`
- **shadcn/ui** components configured in `components.json` (New York style, RSC, TSX)
- **Custom luxury colors**: gold variants, charcoal, champagne, pearl, bronze, cream, granite, platinum
- **Fonts**: Geist Sans/Mono via next/font, Playfair Display and Inter in Tailwind config
- **Utility function**: `cn()` in `app/lib/utils/cn.ts` for class merging

### Animation Libraries
- **Framer Motion** and **GSAP** available via `app/lib/animations/`
- Predefined animation constants: durations (fast, normal, slow, luxury) and easing curves
- Optimized package imports in `next.config.ts`

### Path Aliases
- `@/` maps to `app/` directory
- `@/components` for components
- `@/lib/utils` for utilities

### Performance & Security
- Image optimization configured for luxury real estate photos (WebP, AVIF)
- Security headers (X-Frame-Options, CSP, etc.)
- Console removal in production
- Static asset caching strategies
- Package import optimizations for lucide-react and framer-motion

## Development Guidelines

### Component Patterns
- Use TypeScript with proper interface definitions from `app/data/types.ts`
- Follow shadcn/ui component patterns for consistency
- Leverage the luxury color palette for brand consistency
- Use the `cn()` utility for conditional styling

### Animation Implementation
- Import animation utilities from `app/lib/animations/`
- Use luxury-themed timing and easing for premium feel
- Both Framer Motion and GSAP are available for different use cases

### Data Types
- Property data follows comprehensive schema with images, agent info, specifications
- Search filters and contact forms have defined interfaces
- Neighborhood and transportation data structures available