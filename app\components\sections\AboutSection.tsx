'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils/cn';
import {
  Crown,
  Shield,
  Star,
  Award,
  Users,
  Building,
  MapPin,
  Sparkles,
  Zap,
  Heart,
  Key
} from 'lucide-react';

interface AboutSectionProps {
  className?: string;
}

const luxuryFeatures = [
  {
    icon: Crown,
    title: 'Premium Concierge',
    description: '24/7 dedicated concierge service for all your luxury lifestyle needs'
  },
  {
    icon: Shield,
    title: 'Advanced Security',
    description: 'State-of-the-art biometric access and 24/7 security monitoring'
  },
  {
    icon: Sparkles,
    title: 'Luxury Amenities',
    description: 'Rooftop infinity pool, private gym, and exclusive spa facilities'
  },
  {
    icon: Zap,
    title: 'Smart Living',
    description: 'Fully integrated smart home technology in every residence'
  },
  {
    icon: Heart,
    title: 'Wellness Center',
    description: 'Private wellness sanctuary with meditation gardens and yoga studio'
  },
  {
    icon: Key,
    title: 'Valet Services',
    description: 'Premium valet parking and personalized household services'
  }
];

const achievements = [
  {
    icon: Award,
    number: '50+',
    label: 'Awards Won',
    description: 'International recognition for luxury design'
  },
  {
    icon: Building,
    number: '500+',
    label: 'Residences',
    description: 'Premium homes delivered worldwide'
  },
  {
    icon: Users,
    number: '1000+',
    label: 'Happy Residents',
    description: 'Satisfied luxury homeowners'
  },
  {
    icon: Star,
    number: '25+',
    label: 'Years Experience',
    description: 'Decades of luxury real estate expertise'
  }
];

const architecturalHighlights = [
  'Floor-to-ceiling windows with panoramic city views',
  'Italian marble flooring throughout common areas',
  'Custom millwork and premium finishes',
  'Private elevator access to select residences',
  'European-designed kitchen appliances',
  'Spa-inspired bathrooms with premium fixtures'
];

export function AboutSection({ className }: AboutSectionProps) {
  const [visibleCards, setVisibleCards] = useState<number[]>([]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const cardIndex = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleCards(prev => [...prev, cardIndex]);
          }
        });
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    const cards = document.querySelectorAll('.animate-on-scroll');
    cards.forEach((card) => observer.observe(card));

    return () => observer.disconnect();
  }, []);

  return (
    <section id="about" className={cn('py-24 bg-luxury-cream', className)}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-20">
          <div className="inline-flex items-center space-x-2 bg-luxury-champagne px-4 py-2 rounded-full mb-6">
            <Crown className="h-4 w-4 text-luxury-gold" />
            <span className="luxury-caption text-luxury-gold">About The Sixteen</span>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold luxury-heading mb-6">
            Where Luxury Meets
            <span className="luxury-gradient-text block">Exceptional Living</span>
          </h2>
          <p className="text-xl text-muted-foreground luxury-body leading-relaxed">
            The Sixteen represents the pinnacle of luxury residential living, where architectural excellence meets uncompromising comfort. Our commitment to extraordinary experiences defines every detail of your home.
          </p>
        </div>

        {/* Story Section */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-24">
          <div className="space-y-8">
            <div>
              <h3 className="text-3xl font-bold luxury-heading mb-6">Our Vision</h3>
              <p className="text-lg text-muted-foreground luxury-body leading-relaxed mb-6">
                Born from a vision to redefine luxury living, The Sixteen stands as a testament to architectural innovation and refined taste. Every residence is crafted with meticulous attention to detail, ensuring an unparalleled living experience.
              </p>
              <p className="text-lg text-muted-foreground luxury-body leading-relaxed">
                We believe that luxury is not just about premium materials and exclusive amenities – it&apos;s about creating a lifestyle that enhances every moment of your daily life.
              </p>
            </div>
            <Button 
              variant="luxury"
              size="lg"
              className="luxury-shine-enhanced"
            >
              Discover Our Story
            </Button>
          </div>
          <div className="relative">
            <div className="aspect-[4/3] rounded-2xl overflow-hidden luxury-glass group">
              <Image
                src="https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop"
                alt="Luxury interior design showcase"
                fill
                className="object-cover transition-transform duration-700 group-hover:scale-110"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-luxury-charcoal/70 via-luxury-charcoal/20 to-transparent" />
              <div className="absolute bottom-6 left-6 right-6 text-white">
                <div className="flex items-center space-x-2 mb-3">
                  <Building className="h-5 w-5 text-luxury-gold" />
                  <span className="luxury-caption text-luxury-gold">Premium Architecture</span>
                </div>
                <h4 className="text-xl font-bold mb-2">World-Class Design</h4>
                <p className="text-sm text-white/80">Crafted by internationally acclaimed architects</p>
              </div>
            </div>
          </div>
        </div>

        {/* Luxury Features Grid */}
        <div className="mb-24">
          <div className="text-center mb-16">
            <h3 className="text-3xl md:text-4xl font-bold luxury-heading mb-4">
              Unmatched Luxury Features
            </h3>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Experience a curated collection of premium amenities designed to elevate your lifestyle
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {luxuryFeatures.map((feature, index) => {
              const Icon = feature.icon;
              const isVisible = visibleCards.includes(index);
              return (
                <Card
                  key={feature.title}
                  data-index={index}
                  className={cn(
                    'animate-on-scroll property-glass hover:shadow-xl transition-all duration-500 border-luxury-gold/10',
                    isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                  )}
                  style={{ transitionDelay: `${index * 100}ms` }}
                >
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-luxury-gradient-gold rounded-2xl flex items-center justify-center mx-auto mb-6">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <h4 className="text-xl font-semibold luxury-subheading mb-4">
                      {feature.title}
                    </h4>
                    <p className="text-muted-foreground luxury-body">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Architectural Highlights */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-24">
          <div className="relative order-2 lg:order-1">
            <div className="aspect-[4/3] rounded-2xl overflow-hidden group">
              <Image
                src="https://images.pexels.com/photos/323772/pexels-photo-323772.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop"
                alt="Modern luxury architecture exterior"
                fill
                className="object-cover transition-transform duration-700 group-hover:scale-105"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
              <div className="absolute inset-0 bg-gradient-to-br from-luxury-charcoal/80 via-luxury-charcoal/60 to-luxury-charcoal/40" />
              <div className="absolute inset-0 p-8 flex flex-col justify-center text-white">
                <div className="flex items-center space-x-3 mb-4">
                  <MapPin className="h-6 w-6 text-luxury-gold" />
                  <span className="luxury-caption text-luxury-gold">Architectural Excellence</span>
                </div>
                <h4 className="text-2xl font-bold mb-6">Design Philosophy</h4>
                <ul className="space-y-3">
                  {architecturalHighlights.slice(0, 3).map((highlight, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <Star className="h-5 w-5 text-luxury-gold flex-shrink-0 mt-0.5" />
                      <span className="text-white/90 text-sm leading-relaxed">{highlight}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
          <div className="space-y-8 order-1 lg:order-2">
            <div>
              <h3 className="text-3xl font-bold luxury-heading mb-6">Architectural Highlights</h3>
              <p className="text-lg text-muted-foreground luxury-body leading-relaxed mb-8">
                Every detail at The Sixteen reflects our commitment to architectural excellence. From the grand entrance to the intimate private spaces, each element is designed to inspire and delight.
              </p>
              <ul className="space-y-4">
                {architecturalHighlights.slice(3).map((highlight) => (
                  <li key={highlight} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-luxury-gold rounded-full flex-shrink-0 mt-3" />
                    <span className="text-muted-foreground luxury-body">{highlight}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Achievements Stats */}
        <div className="text-center mb-16">
          <h3 className="text-3xl md:text-4xl font-bold luxury-heading mb-4">
            Excellence in Numbers
          </h3>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Our achievements reflect our unwavering commitment to luxury and excellence
          </p>
        </div>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {achievements.map((achievement, index) => {
            const Icon = achievement.icon;
            return (
              <Card
                key={achievement.label}
                className="property-glass hover:shadow-xl transition-all duration-300 stats-card border-luxury-gold/10"
              >
                <CardContent className="p-8 text-center">
                  <div className="w-12 h-12 bg-luxury-gradient-gold rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="text-3xl font-bold luxury-gradient-text mb-2">
                    {achievement.number}
                  </div>
                  <div className="font-semibold luxury-subheading text-sm mb-2">
                    {achievement.label}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {achievement.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}