'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import PropertyCard from '@/components/common/PropertyCard';
import { 
  Building2, 
  Crown, 
  Gem, 
  Briefcase,
  Filter,
  Grid3X3,
  List,
  MapPin,
  Bed,
  Bath,
  Square,
  Calendar,
  Eye,
  Phone,
  Mail,
  ChevronLeft,
  ChevronRight,
  Star,
  Sparkles,
  Play
} from 'lucide-react';
import { Property } from '@/data/types';
import { luxuryProperties, getPropertiesByType, formatPrice } from '@/data/properties';
import { luxuryAnimations, initGSAP } from '@/lib/animations/gsap';
import { cn } from '@/lib/utils/cn';

interface PropertyShowcaseProps {
  className?: string;
}

type FilterType = 'all' | 'penthouse' | 'luxury' | 'premium' | 'commercial';
type ViewMode = 'grid' | 'list';

const PropertyShowcase: React.FC<PropertyShowcaseProps> = ({ className }) => {
  const [activeFilter, setActiveFilter] = useState<FilterType>('all');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [filteredProperties, setFilteredProperties] = useState<Property[]>(luxuryProperties);

  const sectionRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const filtersRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    initGSAP();
    
    // Animate section entrance
    if (titleRef.current) {
      luxuryAnimations.textReveal(titleRef.current, 0.2);
    }
    
    if (subtitleRef.current) {
      luxuryAnimations.textReveal(subtitleRef.current, 0.4);
    }
    
    if (filtersRef.current) {
      luxuryAnimations.fadeInUp(filtersRef.current, 0.6);
    }
    
    if (gridRef.current) {
      luxuryAnimations.staggerFadeIn(gridRef.current.children, 0.15);
    }
  }, []);

  useEffect(() => {
    const properties = getPropertiesByType(activeFilter);
    setFilteredProperties(properties);
    
    // Re-animate grid when filter changes
    if (gridRef.current) {
      luxuryAnimations.staggerFadeIn(gridRef.current.children, 0.1);
    }
  }, [activeFilter]);

  const filterOptions = [
    { 
      key: 'all' as FilterType, 
      label: 'All Properties', 
      icon: Building2, 
      count: luxuryProperties.length 
    },
    { 
      key: 'penthouse' as FilterType, 
      label: 'Penthouse Suites', 
      icon: Crown, 
      count: getPropertiesByType('penthouse').length 
    },
    { 
      key: 'luxury' as FilterType, 
      label: 'Luxury Apartments', 
      icon: Gem, 
      count: getPropertiesByType('luxury').length 
    },
    { 
      key: 'premium' as FilterType, 
      label: 'Premium Units', 
      icon: Star, 
      count: getPropertiesByType('premium').length 
    },
    { 
      key: 'commercial' as FilterType, 
      label: 'Commercial Spaces', 
      icon: Briefcase, 
      count: getPropertiesByType('commercial').length 
    },
  ];

  const handleViewDetails = (property: Property) => {
    setSelectedProperty(property);
    setCurrentImageIndex(0);
    setIsDialogOpen(true);
  };

  const handleScheduleTour = (property: Property) => {
    // Handle scheduling tour - could open a contact form or redirect
    console.log('Schedule tour for:', property.title);
    // TODO: Implement tour scheduling functionality
  };

  const nextImage = () => {
    if (selectedProperty) {
      setCurrentImageIndex((prev) => 
        prev === selectedProperty.images.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevImage = () => {
    if (selectedProperty) {
      setCurrentImageIndex((prev) => 
        prev === 0 ? selectedProperty.images.length - 1 : prev - 1
      );
    }
  };

  return (
    <section 
      ref={sectionRef}
      className={cn("py-20 bg-gradient-to-br from-luxury-cream via-white to-luxury-pearl", className)}
    >
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 luxury-glass rounded-full px-6 py-3 mb-6 backdrop-blur-sm">
            <Sparkles className="w-4 h-4 text-luxury-gold" />
            <span className="luxury-caption text-luxury-charcoal">Property Collection</span>
            <Sparkles className="w-4 h-4 text-luxury-gold" />
          </div>
          
          <h2 
            ref={titleRef}
            className="luxury-heading text-4xl md:text-5xl lg:text-6xl font-bold text-luxury-charcoal mb-6 leading-tight"
          >
            Discover Your{' '}
            <span className="luxury-gradient-text">Perfect Home</span>
          </h2>
          
          <p 
            ref={subtitleRef}
            className="luxury-subheading text-xl md:text-2xl text-luxury-charcoal/80 max-w-3xl mx-auto leading-relaxed"
          >
            Explore our collection of meticulously designed luxury properties, 
            each offering unparalleled comfort and sophistication in the heart of Rourkela.
          </p>
        </div>

        {/* Filter Controls */}
        <div 
          ref={filtersRef}
          className="flex flex-col lg:flex-row gap-6 items-center justify-between mb-12"
        >
          {/* Property Type Filters */}
          <div className="flex flex-wrap gap-3 items-center">
            <div className="flex items-center gap-2 mr-4">
              <Filter className="w-5 h-5 text-luxury-gold" />
              <span className="luxury-caption text-luxury-charcoal">Filter by:</span>
            </div>
            {filterOptions.map((option) => {
              const Icon = option.icon;
              return (
                <Button
                  key={option.key}
                  variant={activeFilter === option.key ? 'luxury' : 'luxuryOutline'}
                  size="sm"
                  onClick={() => setActiveFilter(option.key)}
                  className="luxury-shine text-sm"
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {option.label}
                  <Badge 
                    variant="outline" 
                    className="ml-2 text-xs bg-white/20 border-white/30"
                  >
                    {option.count}
                  </Badge>
                </Button>
              );
            })}
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center gap-2 luxury-glass rounded-lg p-1 backdrop-blur-sm">
            <Button
              variant={viewMode === 'grid' ? 'luxury' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="h-8 px-3"
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'luxury' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="h-8 px-3"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Properties Grid */}
        <div 
          ref={gridRef}
          className={cn(
            "gap-8 mb-16",
            viewMode === 'grid' 
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3" 
              : "flex flex-col space-y-6"
          )}
        >
          {filteredProperties.map((property) => (
            <PropertyCard
              key={property.id}
              property={property}
              onViewDetails={handleViewDetails}
              onScheduleTour={handleScheduleTour}
              featured={property.featured}
              className={cn(
                "property-card",
                viewMode === 'list' && "flex-row max-w-none"
              )}
            />
          ))}
        </div>

        {/* Results Summary */}
        <div className="text-center">
          <p className="luxury-body text-luxury-charcoal/70 mb-6">
            Showing <span className="font-semibold text-luxury-gold">{filteredProperties.length}</span> {' '}
            {activeFilter === 'all' ? 'luxury properties' : filterOptions.find(f => f.key === activeFilter)?.label.toLowerCase()}
          </p>
          
          <Button variant="luxuryOutline" size="lg" className="luxury-shine">
            <Building2 className="w-5 h-5 mr-2" />
            View Full Portfolio
          </Button>
        </div>
      </div>

      {/* Property Details Modal */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto luxury-glass border-luxury-gold/20">
          {selectedProperty && (
            <>
              <DialogHeader className="pb-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <DialogTitle className="luxury-heading text-2xl md:text-3xl text-luxury-charcoal">
                        {selectedProperty.title}
                      </DialogTitle>
                      {selectedProperty.featured && (
                        <Badge className="bg-luxury-gold text-white">
                          <Star className="w-3 h-3 mr-1" />
                          Featured
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center text-luxury-charcoal/70 mb-4">
                      <MapPin className="w-4 h-4 mr-2 text-luxury-gold" />
                      <span>{selectedProperty.address.street}, {selectedProperty.address.city}</span>
                    </div>
                    <div className="text-3xl font-bold luxury-gradient-text">
                      {formatPrice(selectedProperty.price)}
                    </div>
                  </div>
                </div>
                <DialogDescription className="luxury-body text-luxury-charcoal/80 leading-relaxed">
                  {selectedProperty.description}
                </DialogDescription>
              </DialogHeader>

              <div className="grid lg:grid-cols-2 gap-8">
                {/* Image Gallery */}
                <div className="space-y-4">
                  <div className="relative h-80 lg:h-96 rounded-lg overflow-hidden">
                    <Image
                      src={selectedProperty.images[currentImageIndex]?.url || '/images/properties/placeholder.svg'}
                      alt={selectedProperty.images[currentImageIndex]?.alt || selectedProperty.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 50vw"
                    />
                    
                    {/* Navigation */}
                    {selectedProperty.images.length > 1 && (
                      <>
                        <button
                          onClick={prevImage}
                          className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                        >
                          <ChevronLeft className="w-5 h-5 text-white" />
                        </button>
                        <button
                          onClick={nextImage}
                          className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                        >
                          <ChevronRight className="w-5 h-5 text-white" />
                        </button>
                      </>
                    )}

                    {/* Virtual Tour & Video Buttons */}
                    <div className="absolute bottom-4 right-4 flex gap-2">
                      {selectedProperty.video && (
                        <Button size="sm" variant="luxury" className="h-8">
                          <Play className="w-3 h-3 mr-1" />
                          Video Tour
                        </Button>
                      )}
                      {selectedProperty.virtualTour && (
                        <Button size="sm" variant="luxuryOutline" className="h-8 bg-white/20 backdrop-blur-sm">
                          <Eye className="w-3 h-3 mr-1" />
                          360° Tour
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Image Thumbnails */}
                  {selectedProperty.images.length > 1 && (
                    <div className="flex gap-2 overflow-x-auto pb-2">
                      {selectedProperty.images.map((image, index) => (
                        <button
                          key={image.id}
                          onClick={() => setCurrentImageIndex(index)}
                          className={cn(
                            "relative w-20 h-16 rounded-md overflow-hidden flex-shrink-0 border-2 transition-colors",
                            index === currentImageIndex 
                              ? "border-luxury-gold" 
                              : "border-luxury-gold/20 hover:border-luxury-gold/40"
                          )}
                        >
                          <Image
                            src={image.url}
                            alt={image.alt}
                            fill
                            className="object-cover"
                            sizes="80px"
                          />
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Property Details */}
                <div className="space-y-6">
                  {/* Specifications */}
                  <Card className="p-6 luxury-glass border-luxury-gold/20">
                    <h3 className="luxury-heading text-lg font-semibold mb-4 text-luxury-charcoal">
                      Property Specifications
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      {selectedProperty.specifications.bedrooms > 0 && (
                        <div className="flex items-center gap-2">
                          <Bed className="w-5 h-5 text-luxury-gold" />
                          <span className="luxury-body">{selectedProperty.specifications.bedrooms} Bedrooms</span>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Bath className="w-5 h-5 text-luxury-gold" />
                        <span className="luxury-body">{selectedProperty.specifications.bathrooms} Bathrooms</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Square className="w-5 h-5 text-luxury-gold" />
                        <span className="luxury-body">{selectedProperty.specifications.squareFeet.toLocaleString()} sq ft</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-5 h-5 text-luxury-gold" />
                        <span className="luxury-body">Built {selectedProperty.specifications.yearBuilt}</span>
                      </div>
                    </div>
                  </Card>

                  {/* Features */}
                  <Card className="p-6 luxury-glass border-luxury-gold/20">
                    <h3 className="luxury-heading text-lg font-semibold mb-4 text-luxury-charcoal">
                      Premium Features
                    </h3>
                    <div className="grid grid-cols-1 gap-2">
                      {selectedProperty.features.slice(0, 8).map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-luxury-gold rounded-full" />
                          <span className="luxury-body text-sm">{feature}</span>
                        </div>
                      ))}
                      {selectedProperty.features.length > 8 && (
                        <div className="mt-2">
                          <Badge variant="outline" className="text-luxury-gold border-luxury-gold/20">
                            +{selectedProperty.features.length - 8} more features
                          </Badge>
                        </div>
                      )}
                    </div>
                  </Card>

                  {/* Agent Information */}
                  <Card className="p-6 luxury-glass border-luxury-gold/20">
                    <h3 className="luxury-heading text-lg font-semibold mb-4 text-luxury-charcoal">
                      Your Luxury Property Consultant
                    </h3>
                    <div className="flex items-start gap-4">
                      <div className="w-16 h-16 bg-luxury-gradient-gold rounded-full flex items-center justify-center">
                        <span className="text-white font-semibold text-lg">
                          {selectedProperty.agent.name.charAt(0)}
                        </span>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-luxury-charcoal">{selectedProperty.agent.name}</h4>
                        <p className="text-sm text-luxury-charcoal/70 mb-2">{selectedProperty.agent.title}</p>
                        <div className="flex flex-col gap-1">
                          <a 
                            href={`tel:${selectedProperty.agent.phone}`}
                            className="flex items-center gap-2 text-sm text-luxury-gold hover:text-luxury-gold-dark transition-colors"
                          >
                            <Phone className="w-4 h-4" />
                            {selectedProperty.agent.phone}
                          </a>
                          <a 
                            href={`mailto:${selectedProperty.agent.email}`}
                            className="flex items-center gap-2 text-sm text-luxury-gold hover:text-luxury-gold-dark transition-colors"
                          >
                            <Mail className="w-4 h-4" />
                            {selectedProperty.agent.email}
                          </a>
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* Action Buttons */}
                  <div className="flex gap-3">
                    <Button 
                      variant="luxury" 
                      size="lg" 
                      className="flex-1 luxury-shine"
                      onClick={() => handleScheduleTour(selectedProperty)}
                    >
                      <Calendar className="w-5 h-5 mr-2" />
                      Schedule Private Tour
                    </Button>
                    <Button variant="luxuryOutline" size="lg">
                      <Phone className="w-5 h-5 mr-2" />
                      Call Now
                    </Button>
                  </div>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default PropertyShowcase;