'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  Calculator, 
  PiggyBank, 
  CreditCard,
  Building2,
  DollarSign,
  Shield,
  Award,
  Banknote,
  Receipt,
  FileText,
  Percent,
  ArrowUpRight,
  CheckCircle,
  Users,
  Clock
} from 'lucide-react';
import { luxuryAnimations, initGSAP } from '@/lib/animations/gsap';
import { cn } from '@/lib/utils/cn';

interface PricingPlan {
  type: '2BHK' | '3BHK' | '4BHK' | 'Penthouse';
  area: string;
  basePrice: number;
  pricePerSqFt: number;
  totalUnits: number;
  availableUnits: number;
  floors: string;
  features: string[];
  popular?: boolean;
}

interface FinancingOption {
  bank: string;
  logo?: string;
  interestRate: string;
  loanAmount: string;
  tenure: string;
  features: string[];
  specialOffer?: string;
}

interface InvestmentBenefit {
  icon: React.ReactNode;
  title: string;
  description: string;
  value: string;
  trend?: 'up' | 'down';
}

const PricingCard: React.FC<{ plan: PricingPlan; index: number }> = ({ plan, index }) => {
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const node = cardRef.current;
    if (!node) return;

    const hoverAnimation = luxuryAnimations.luxuryHover(node);
    const handleMouseEnter = () => hoverAnimation.play();
    const handleMouseLeave = () => hoverAnimation.reverse();

    node.addEventListener('mouseenter', handleMouseEnter);
    node.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      node.removeEventListener('mouseenter', handleMouseEnter);
      node.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  const formatPrice = (price: number) => {
    if (price >= 10000000) {
      return `₹${(price / 10000000).toFixed(1)} Cr`;
    } else if (price >= 100000) {
      return `₹${(price / 100000).toFixed(1)} L`;
    }
    return `₹${price.toLocaleString()}`;
  };

  const availabilityPercentage = (plan.availableUnits / plan.totalUnits) * 100;

  return (
    <Card 
      ref={cardRef}
      className={cn(
        "luxury-glass backdrop-blur-sm p-8 border transition-all duration-500 relative",
        plan.popular 
          ? "border-luxury-gold shadow-2xl shadow-luxury-gold/20 scale-105" 
          : "border-luxury-gold/20 hover:border-luxury-gold/40"
      )}
    >
      {/* Popular Badge */}
      {plan.popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <div className="bg-gradient-to-r from-luxury-gold to-luxury-gold-light px-6 py-2 rounded-full">
            <span className="text-white font-semibold text-sm">Most Popular</span>
          </div>
        </div>
      )}

      {/* Unit Type & Area */}
      <div className="text-center mb-6">
        <h3 className="luxury-subheading text-2xl font-bold text-white mb-2">
          {plan.type}
        </h3>
        <p className="luxury-body text-luxury-gold text-lg">
          {plan.area}
        </p>
        <p className="luxury-caption text-white/60 text-sm">
          Floors: {plan.floors}
        </p>
      </div>

      {/* Pricing */}
      <div className="text-center mb-6 pb-6 border-b border-luxury-gold/20">
        <div className="luxury-heading text-3xl font-bold text-white mb-1">
          {formatPrice(plan.basePrice)}
        </div>
        <p className="luxury-caption text-white/70">
          {formatPrice(plan.pricePerSqFt)}/sq ft
        </p>
      </div>

      {/* Availability */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="luxury-caption text-white/80">Availability</span>
          <span className="luxury-caption text-luxury-gold font-semibold">
            {plan.availableUnits}/{plan.totalUnits} units
          </span>
        </div>
        <div className="w-full bg-luxury-charcoal-light rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-luxury-gold to-luxury-gold-light h-2 rounded-full transition-all duration-1000"
            style={{ width: `${100 - availabilityPercentage}%` }}
          />
        </div>
        <p className="luxury-caption text-white/60 text-xs mt-1">
          {availabilityPercentage.toFixed(0)}% available
        </p>
      </div>

      {/* Features */}
      <div className="mb-8">
        <h4 className="luxury-caption text-white font-semibold mb-3">Key Features:</h4>
        <ul className="space-y-2">
          {plan.features.map((feature, idx) => (
            <li key={idx} className="flex items-center gap-2">
              <CheckCircle className="w-3 h-3 text-luxury-gold flex-shrink-0" />
              <span className="luxury-caption text-white/80 text-sm">{feature}</span>
            </li>
          ))}
        </ul>
      </div>

      {/* CTA Button */}
      <Button
        variant={plan.popular ? "luxury" : "luxuryOutline"}
        size="lg"
        className="w-full luxury-shine-enhanced"
      >
        <Calculator className="w-4 h-4" />
        Calculate EMI
      </Button>
    </Card>
  );
};

const FinancingCard: React.FC<{ option: FinancingOption }> = ({ option }) => {
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const node = cardRef.current;
    if (!node) return;

    const hoverAnimation = luxuryAnimations.luxuryHover(node);
    const handleMouseEnter = () => hoverAnimation.play();
    const handleMouseLeave = () => hoverAnimation.reverse();

    node.addEventListener('mouseenter', handleMouseEnter);
    node.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      node.removeEventListener('mouseenter', handleMouseEnter);
      node.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  return (
    <Card 
      ref={cardRef}
      className="luxury-glass backdrop-blur-sm p-6 border border-luxury-gold/20 hover:border-luxury-gold/40 transition-all duration-500"
    >
      {/* Special Offer Badge */}
      {option.specialOffer && (
        <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-2 mb-4">
          <p className="luxury-caption text-green-400 text-sm text-center">
            {option.specialOffer}
          </p>
        </div>
      )}

      {/* Bank Info */}
      <div className="text-center mb-6">
        <h3 className="luxury-subheading text-xl font-semibold text-white mb-2">
          {option.bank}
        </h3>
        <div className="luxury-heading text-2xl font-bold text-luxury-gold">
          {option.interestRate}
        </div>
        <p className="luxury-caption text-white/70 text-sm">Interest Rate</p>
      </div>

      {/* Loan Details */}
      <div className="space-y-3 mb-6">
        <div className="flex items-center justify-between">
          <span className="luxury-caption text-white/80">Max Loan Amount</span>
          <span className="luxury-caption text-white font-semibold">{option.loanAmount}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="luxury-caption text-white/80">Tenure</span>
          <span className="luxury-caption text-white font-semibold">{option.tenure}</span>
        </div>
      </div>

      {/* Features */}
      <div className="mb-6">
        <ul className="space-y-2">
          {option.features.map((feature, idx) => (
            <li key={idx} className="flex items-center gap-2">
              <CheckCircle className="w-3 h-3 text-luxury-gold flex-shrink-0" />
              <span className="luxury-caption text-white/80 text-sm">{feature}</span>
            </li>
          ))}
        </ul>
      </div>

      <Button variant="luxuryOutline" size="sm" className="w-full">
        <CreditCard className="w-4 h-4" />
        Apply Now
      </Button>
    </Card>
  );
};

const BenefitCard: React.FC<{ benefit: InvestmentBenefit; index: number }> = ({ benefit, index }) => {
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (cardRef.current) {
      luxuryAnimations.fadeInUp(cardRef.current, index * 0.1);
    }
  }, [index]);

  return (
    <Card 
      ref={cardRef}
      className="luxury-glass backdrop-blur-sm p-6 border border-luxury-gold/20 text-center"
    >
      <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-luxury-gold to-luxury-gold-light rounded-2xl flex items-center justify-center">
        {benefit.icon}
      </div>
      
      <h3 className="luxury-subheading text-lg font-semibold text-white mb-2">
        {benefit.title}
      </h3>
      
      <p className="luxury-body text-white/70 text-sm mb-4 leading-relaxed">
        {benefit.description}
      </p>
      
      <div className="flex items-center justify-center gap-2">
        <span className="luxury-heading text-2xl font-bold text-luxury-gold">
          {benefit.value}
        </span>
        {benefit.trend === 'up' && (
          <ArrowUpRight className="w-5 h-5 text-green-400" />
        )}
      </div>
    </Card>
  );
};

const InvestmentSection: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const pricingRef = useRef<HTMLDivElement>(null);
  const financingRef = useRef<HTMLDivElement>(null);
  const benefitsRef = useRef<HTMLDivElement>(null);
  const calculatorRef = useRef<HTMLDivElement>(null);

  const [activeTab, setActiveTab] = useState<'pricing' | 'financing' | 'calculator'>('pricing');

  useEffect(() => {
    initGSAP();

    if (headerRef.current) {
      luxuryAnimations.fadeInUp(headerRef.current, 0.2);
    }

    if (pricingRef.current) {
      luxuryAnimations.staggerFadeIn(pricingRef.current.children, 0.1);
    }

    if (benefitsRef.current) {
      luxuryAnimations.staggerFadeIn(benefitsRef.current.children, 0.15);
    }
  }, []);

  const pricingPlans: PricingPlan[] = [
    {
      type: '2BHK',
      area: '1,200 - 1,400 sq ft',
      basePrice: 4500000,
      pricePerSqFt: 3500,
      totalUnits: 48,
      availableUnits: 12,
      floors: '3rd - 8th',
      features: [
        'Premium finishes',
        'Modular kitchen',
        'Designer bathrooms',
        'Private balcony',
        'Smart home ready'
      ]
    },
    {
      type: '3BHK',
      area: '1,800 - 2,200 sq ft',
      basePrice: 7200000,
      pricePerSqFt: 3600,
      totalUnits: 36,
      availableUnits: 18,
      floors: '4th - 12th',
      features: [
        'Spacious living areas',
        'Master bedroom suite',
        'Premium appliances',
        'Study room',
        'Multiple balconies'
      ],
      popular: true
    },
    {
      type: '4BHK',
      area: '2,800 - 3,200 sq ft',
      basePrice: 11500000,
      pricePerSqFt: 3800,
      totalUnits: 24,
      availableUnits: 8,
      floors: '8th - 14th',
      features: [
        'Luxury interior design',
        'Private lift access',
        'Home office space',
        'Family lounge',
        'Premium fixtures'
      ]
    },
    {
      type: 'Penthouse',
      area: '4,500 - 6,000 sq ft',
      basePrice: ********,
      pricePerSqFt: 4500,
      totalUnits: 8,
      availableUnits: 3,
      floors: '15th - 16th',
      features: [
        'Duplex design',
        'Private terrace',
        'Panoramic views',
        'Private pool option',
        'Concierge service'
      ]
    }
  ];

  const financingOptions: FinancingOption[] = [
    {
      bank: 'SBI Home Loans',
      interestRate: '8.50%',
      loanAmount: '₹5 Cr',
      tenure: '30 Years',
      features: [
        'No prepayment charges',
        'Quick approval process',
        'Flexible repayment options',
        'Special rates for salaried'
      ],
      specialOffer: 'Special rate for The Sixteen customers'
    },
    {
      bank: 'HDFC Housing Finance',
      interestRate: '8.75%',
      loanAmount: '₹5 Cr',
      tenure: '30 Years',
      features: [
        'Digital processing',
        'Doorstep service',
        'Balance transfer facility',
        'Insurance benefits'
      ]
    },
    {
      bank: 'ICICI Bank',
      interestRate: '8.65%',
      loanAmount: '₹3 Cr',
      tenure: '25 Years',
      features: [
        'Online application',
        'Quick disbursal',
        'Step-up EMI facility',
        'Tax benefits guidance'
      ],
      specialOffer: 'Zero processing fee for first 100 customers'
    }
  ];

  const investmentBenefits: InvestmentBenefit[] = [
    {
      icon: <TrendingUp className="w-8 h-8 text-white" />,
      title: 'Capital Appreciation',
      description: 'Expected annual growth in property value based on Rourkela market trends',
      value: '12-15%',
      trend: 'up'
    },
    {
      icon: <PiggyBank className="w-8 h-8 text-white" />,
      title: 'Rental Yield',
      description: 'Attractive rental returns in premium Rourkela location',
      value: '6-8%',
      trend: 'up'
    },
    {
      icon: <Shield className="w-8 h-8 text-white" />,
      title: 'Price Protection',
      description: 'No price escalation guarantee until possession',
      value: '100%',
    },
    {
      icon: <Award className="w-8 h-8 text-white" />,
      title: 'Possession Guarantee',
      description: 'Assured possession within committed timeframe',
      value: '36 Months'
    }
  ];

  return (
    <section 
      ref={sectionRef}
      id="investment"
      className="relative py-20 lg:py-32 bg-luxury-charcoal-light overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-luxury-charcoal-light via-luxury-charcoal to-luxury-charcoal-dark" />
      <div className="absolute top-0 left-0 w-full h-full opacity-5 bg-[url('https://images.pexels.com/photos/323772/pexels-photo-323772.jpeg?auto=compress&cs=tinysrgb&w=1920')] bg-cover bg-center" />
      
      {/* Decorative Elements */}
      <div className="absolute top-20 right-10 w-2 h-32 bg-gradient-to-b from-luxury-gold to-transparent opacity-60" />
      <div className="absolute bottom-20 left-10 w-2 h-32 bg-gradient-to-t from-luxury-gold to-transparent opacity-60" />

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div ref={headerRef} className="text-center mb-16">
          <div className="inline-flex items-center gap-2 luxury-glass rounded-full px-6 py-3 mb-6 backdrop-blur-sm">
            <DollarSign className="w-4 h-4 text-luxury-gold" />
            <span className="luxury-caption text-white">Investment Opportunity</span>
          </div>
          
          <h2 className="luxury-heading text-4xl md:text-6xl font-bold text-white mb-6">
            Smart{' '}
            <span className="luxury-gradient-text">Investment</span>
            {' '}Choice
          </h2>
          
          <p className="luxury-subheading text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Discover exceptional investment opportunities with attractive returns and flexible financing options
          </p>
        </div>

        {/* Investment Benefits */}
        <div ref={benefitsRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {investmentBenefits.map((benefit, index) => (
            <BenefitCard key={index} benefit={benefit} index={index} />
          ))}
        </div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-12">
          <div className="luxury-glass rounded-full p-2 backdrop-blur-sm">
            <div className="flex gap-2">
              <Button
                variant={activeTab === 'pricing' ? 'luxury' : 'ghost'}
                onClick={() => setActiveTab('pricing')}
                className={cn(
                  "rounded-full transition-all duration-300",
                  activeTab === 'pricing' 
                    ? "shadow-lg shadow-luxury-gold/25" 
                    : "text-white/70 hover:text-white"
                )}
              >
                <Building2 className="w-4 h-4" />
                Pricing Plans
              </Button>
              <Button
                variant={activeTab === 'financing' ? 'luxury' : 'ghost'}
                onClick={() => setActiveTab('financing')}
                className={cn(
                  "rounded-full transition-all duration-300",
                  activeTab === 'financing' 
                    ? "shadow-lg shadow-luxury-gold/25" 
                    : "text-white/70 hover:text-white"
                )}
              >
                <CreditCard className="w-4 h-4" />
                Financing Options
              </Button>
              <Button
                variant={activeTab === 'calculator' ? 'luxury' : 'ghost'}
                onClick={() => setActiveTab('calculator')}
                className={cn(
                  "rounded-full transition-all duration-300",
                  activeTab === 'calculator' 
                    ? "shadow-lg shadow-luxury-gold/25" 
                    : "text-white/70 hover:text-white"
                )}
              >
                <Calculator className="w-4 h-4" />
                EMI Calculator
              </Button>
            </div>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'pricing' && (
          <div ref={pricingRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {pricingPlans.map((plan, index) => (
              <PricingCard key={index} plan={plan} index={index} />
            ))}
          </div>
        )}

        {activeTab === 'financing' && (
          <div ref={financingRef} className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {financingOptions.map((option, index) => (
              <FinancingCard key={index} option={option} />
            ))}
          </div>
        )}

        {activeTab === 'calculator' && (
          <div ref={calculatorRef} className="max-w-4xl mx-auto mb-16">
            <Card className="luxury-glass backdrop-blur-sm p-8 border border-luxury-gold/20">
              <div className="text-center mb-8">
                <Calculator className="w-16 h-16 text-luxury-gold mx-auto mb-4" />
                <h3 className="luxury-subheading text-2xl font-semibold text-white mb-2">
                  EMI Calculator
                </h3>
                <p className="luxury-body text-white/70">
                  Calculate your monthly EMI and plan your investment
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div>
                    <label className="luxury-caption text-white/80 mb-2 block">
                      Loan Amount (₹)
                    </label>
                    <div className="bg-luxury-charcoal-light border border-luxury-gold/20 rounded-lg p-4">
                      <input
                        type="range"
                        min="1000000"
                        max="50000000"
                        step="100000"
                        className="w-full"
                      />
                      <div className="flex justify-between text-sm text-white/60 mt-2">
                        <span>₹10L</span>
                        <span className="text-luxury-gold font-semibold">₹50L</span>
                        <span>₹5Cr</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="luxury-caption text-white/80 mb-2 block">
                      Interest Rate (%)
                    </label>
                    <div className="bg-luxury-charcoal-light border border-luxury-gold/20 rounded-lg p-4">
                      <input
                        type="range"
                        min="7"
                        max="12"
                        step="0.1"
                        className="w-full"
                      />
                      <div className="flex justify-between text-sm text-white/60 mt-2">
                        <span>7%</span>
                        <span className="text-luxury-gold font-semibold">8.5%</span>
                        <span>12%</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="luxury-caption text-white/80 mb-2 block">
                      Loan Tenure (Years)
                    </label>
                    <div className="bg-luxury-charcoal-light border border-luxury-gold/20 rounded-lg p-4">
                      <input
                        type="range"
                        min="5"
                        max="30"
                        step="1"
                        className="w-full"
                      />
                      <div className="flex justify-between text-sm text-white/60 mt-2">
                        <span>5 years</span>
                        <span className="text-luxury-gold font-semibold">20 years</span>
                        <span>30 years</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-luxury-charcoal-light border border-luxury-gold/20 rounded-lg p-6">
                  <h4 className="luxury-subheading text-lg font-semibold text-white mb-6">
                    EMI Breakdown
                  </h4>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="luxury-body text-white/80">Monthly EMI</span>
                      <span className="luxury-heading text-xl font-bold text-luxury-gold">
                        ₹45,678
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="luxury-body text-white/80">Total Interest</span>
                      <span className="luxury-body text-white font-semibold">
                        ₹64,43,680
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="luxury-body text-white/80">Total Amount</span>
                      <span className="luxury-body text-white font-semibold">
                        ₹1,64,43,680
                      </span>
                    </div>
                  </div>

                  <div className="mt-6 pt-6 border-t border-luxury-gold/20">
                    <Button variant="luxury" size="lg" className="w-full">
                      <FileText className="w-4 h-4" />
                      Get Pre-Approved
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Final CTA */}
        <div className="text-center">
          <Card className="luxury-glass backdrop-blur-sm p-8 border border-luxury-gold/20 max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <Clock className="w-8 h-8 text-luxury-gold mx-auto mb-2" />
                <div className="luxury-heading text-xl font-bold text-white">48 Hours</div>
                <div className="luxury-caption text-white/70">Loan Pre-Approval</div>
              </div>
              
              <div className="text-center">
                <Users className="w-8 h-8 text-luxury-gold mx-auto mb-2" />
                <div className="luxury-heading text-xl font-bold text-white">15+</div>
                <div className="luxury-caption text-white/70">Banking Partners</div>
              </div>
              
              <div className="text-center">
                <Percent className="w-8 h-8 text-luxury-gold mx-auto mb-2" />
                <div className="luxury-heading text-xl font-bold text-white">Zero</div>
                <div className="luxury-caption text-white/70">Hidden Charges</div>
              </div>
            </div>

            <h3 className="luxury-subheading text-2xl font-semibold text-white mb-4">
              Ready to Invest in Your Future?
            </h3>
            <p className="luxury-body text-white/80 mb-6">
              Our investment advisors are ready to help you make the smart choice for your portfolio.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="luxury" size="xl" className="luxury-shine-enhanced">
                <Banknote className="w-5 h-5" />
                Speak to Investment Advisor
              </Button>
              <Button variant="luxuryOutline" size="xl">
                <Receipt className="w-5 h-5" />
                Download Price List
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default InvestmentSection;