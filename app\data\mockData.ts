import { Property, Agent, Neighborhood } from './types';

// Mock data for development and demo purposes

export const mockAgents: Agent[] = [
  {
    id: 'agent-1',
    name: '<PERSON>',
    title: 'Luxury Real Estate Specialist',
    email: '<EMAIL>',
    phone: '+****************',
    image: '/images/agents/victoria-sterling.jpg',
    bio: 'With over 15 years of experience in luxury real estate, Victoria has facilitated over $500M in high-end property transactions.',
    specialties: ['Penthouses', 'Luxury Condos', 'Investment Properties'],
    languages: ['English', 'French', 'Spanish'],
    socialMedia: {
      linkedin: 'https://linkedin.com/in/victoriastarling',
      instagram: 'https://instagram.com/victoriastarling',
    },
  },
  {
    id: 'agent-2',
    name: '<PERSON>',
    title: 'Senior Estate Advisor',
    email: '<EMAIL>',
    phone: '+****************',
    image: '/images/agents/alexander-blackwood.jpg',
    bio: '<PERSON> specializes in ultra-luxury estates and has been recognized as a top 1% realtor for five consecutive years.',
    specialties: ['Luxury Estates', 'Historic Properties', 'Waterfront Homes'],
    languages: ['English', 'Italian'],
    socialMedia: {
      linkedin: 'https://linkedin.com/in/alexanderblackwood',
    },
  },
];

export const mockNeighborhoods: Neighborhood[] = [
  {
    id: 'upper-east-side',
    name: 'Upper East Side',
    description: 'The epitome of luxury living with world-class museums, high-end shopping, and prestigious addresses.',
    image: '/images/neighborhoods/upper-east-side.jpg',
    averagePrice: 8500000,
    propertyCount: 24,
    highlights: ['Museum Mile', 'Central Park Access', 'Prestigious Schools'],
    amenities: ['Fine Dining', 'Luxury Shopping', 'Art Galleries', 'Private Clubs'],
    schools: [
      { name: 'The Brearley School', type: 'private', rating: 9.8, distance: 0.3 },
      { name: 'Hunter College Elementary', type: 'elementary', rating: 9.5, distance: 0.5 },
    ],
    transportation: [
      { type: 'subway', name: '6 Train - 77th St', distance: 0.2, walkTime: 3 },
      { type: 'subway', name: 'Q Train - 72nd St', distance: 0.4, walkTime: 5 },
    ],
  },
  {
    id: 'tribeca',
    name: 'Tribeca',
    description: 'Historic charm meets modern luxury in this coveted downtown neighborhood.',
    image: '/images/neighborhoods/tribeca.jpg',
    averagePrice: 7200000,
    propertyCount: 18,
    highlights: ['Historic Architecture', 'Celebrity Residents', 'Cobblestone Streets'],
    amenities: ['Michelin-Starred Restaurants', 'Art Galleries', 'Private Parks'],
    schools: [
      { name: 'PS 234', type: 'elementary', rating: 9.2, distance: 0.4 },
      { name: 'Stuyvesant High School', type: 'high', rating: 9.7, distance: 1.2 },
    ],
    transportation: [
      { type: 'subway', name: '1 Train - Franklin St', distance: 0.3, walkTime: 4 },
      { type: 'subway', name: '6 Train - Canal St', distance: 0.6, walkTime: 8 },
    ],
  },
];

export const mockProperties: Property[] = [
  {
    id: 'prop-1',
    title: 'Penthouse Paradise at The Sterling Tower',
    description: 'An extraordinary penthouse offering unparalleled luxury and breathtaking 360-degree city views. This magnificent residence features floor-to-ceiling windows, private outdoor terraces, and the finest finishes throughout.',
    price: 12500000,
    address: {
      street: '432 Park Avenue, PH-A',
      city: 'New York',
      state: 'NY',
      zipCode: '10016',
      country: 'USA',
    },
    specifications: {
      bedrooms: 4,
      bathrooms: 5,
      squareFeet: 4200,
      yearBuilt: 2023,
      propertyType: 'penthouse',
    },
    features: [
      'Private Elevator Access',
      'Floor-to-Ceiling Windows',
      'Private Outdoor Terraces',
      'Smart Home Technology',
      'Wine Cellar',
      'Home Theater',
    ],
    amenities: [
      '24/7 Concierge',
      'Fitness Center',
      'Rooftop Pool',
      'Private Dining Room',
      'Business Center',
      'Valet Parking',
    ],
    images: [
      {
        id: 'img-1',
        url: '/images/properties/penthouse-1-main.jpg',
        alt: 'Penthouse living room with city views',
        type: 'interior',
        featured: true,
        order: 1,
      },
      {
        id: 'img-2',
        url: '/images/properties/penthouse-1-kitchen.jpg',
        alt: 'Gourmet kitchen with marble countertops',
        type: 'interior',
        featured: false,
        order: 2,
      },
      {
        id: 'img-3',
        url: '/images/properties/penthouse-1-terrace.jpg',
        alt: 'Private terrace with city skyline',
        type: 'exterior',
        featured: false,
        order: 3,
      },
    ],
    agent: mockAgents[0]!,
    status: 'available',
    featured: true,
    luxury: true,
    coordinates: {
      lat: 40.7505,
      lng: -73.9806,
    },
    virtualTour: 'https://virtual-tour.example.com/penthouse-1',
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
  },
  {
    id: 'prop-2',
    title: 'Tribeca Loft Masterpiece',
    description: 'A stunning loft conversion in the heart of Tribeca, featuring soaring ceilings, exposed brick walls, and an open-concept design that seamlessly blends historic charm with modern luxury.',
    price: 8750000,
    address: {
      street: '125 Hudson Street, Unit 3B',
      city: 'New York',
      state: 'NY',
      zipCode: '10013',
      country: 'USA',
    },
    specifications: {
      bedrooms: 3,
      bathrooms: 3,
      squareFeet: 3200,
      yearBuilt: 1920,
      propertyType: 'loft',
    },
    features: [
      'Exposed Brick Walls',
      '14-Foot Ceilings',
      'Original Hardwood Floors',
      'Chef\'s Kitchen',
      'Master Suite with Walk-in Closet',
      'Home Office',
    ],
    amenities: [
      'Doorman',
      'Fitness Center',
      'Roof Deck',
      'Storage',
      'Bike Room',
    ],
    images: [
      {
        id: 'img-4',
        url: '/images/properties/loft-1-main.jpg',
        alt: 'Open loft space with exposed brick',
        type: 'interior',
        featured: true,
        order: 1,
      },
      {
        id: 'img-5',
        url: '/images/properties/loft-1-kitchen.jpg',
        alt: 'Industrial-style kitchen',
        type: 'interior',
        featured: false,
        order: 2,
      },
    ],
    agent: mockAgents[1]!,
    status: 'available',
    featured: true,
    luxury: true,
    coordinates: {
      lat: 40.7195,
      lng: -74.0089,
    },
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-18T16:45:00Z',
  },
  {
    id: 'prop-3',
    title: 'Upper East Side Mansion',
    description: 'An exceptional townhouse mansion on one of the Upper East Side\'s most prestigious blocks. This elegant residence offers timeless luxury with modern conveniences.',
    price: 15200000,
    address: {
      street: '15 East 72nd Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10021',
      country: 'USA',
    },
    specifications: {
      bedrooms: 6,
      bathrooms: 7,
      squareFeet: 8500,
      lotSize: 2000,
      yearBuilt: 1925,
      propertyType: 'mansion',
    },
    features: [
      'Grand Staircase',
      'Library with Built-in Shelving',
      'Formal Dining Room',
      'Private Garden',
      'Wine Cellar',
      'Staff Quarters',
      'Elevator',
    ],
    amenities: [
      'Private Garden',
      'Garage Parking',
      'Security System',
      'Central Air Conditioning',
    ],
    images: [
      {
        id: 'img-6',
        url: '/images/properties/mansion-1-main.jpg',
        alt: 'Elegant mansion exterior',
        type: 'exterior',
        featured: true,
        order: 1,
      },
    ],
    agent: mockAgents[0]!,
    status: 'available',
    featured: true,
    luxury: true,
    coordinates: {
      lat: 40.7710,
      lng: -73.9655,
    },
    createdAt: '2024-01-05T12:00:00Z',
    updatedAt: '2024-01-22T09:15:00Z',
  },
];

// Helper functions for mock data
export const getFeaturedProperties = () => mockProperties.filter(p => p.featured);
export const getLuxuryProperties = () => mockProperties.filter(p => p.luxury);
export const getPropertyById = (id: string) => mockProperties.find(p => p.id === id);
export const getAgentById = (id: string) => mockAgents.find(a => a.id === id);
export const getNeighborhoodById = (id: string) => mockNeighborhoods.find(n => n.id === id);