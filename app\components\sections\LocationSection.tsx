'use client';

import React, { useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { 
  MapPin, 
  Clock, 
  Train, 
  Car, 
  Plane, 
  GraduationCap, 
  Building, 
  TreePine,
  Navigation,
  Star
} from 'lucide-react';
import { luxuryAnimations, initGSAP } from '@/lib/animations/gsap';
import { cn } from '@/lib/utils/cn';

interface LandmarkProps {
  name: string;
  type: 'educational' | 'industrial' | 'recreational' | 'commercial';
  distance: string;
  walkTime: string;
  icon: React.ReactNode;
  description?: string;
}

interface TransportProps {
  type: 'train' | 'airport' | 'highway';
  name: string;
  distance: string;
  duration: string;
  icon: React.ReactNode;
}

const LandmarkCard: React.FC<LandmarkProps> = ({ 
  name, 
  type, 
  distance, 
  walkTime, 
  icon, 
  description 
}) => {
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const node = cardRef.current;
    if (!node) return;

    const hoverAnimation = luxuryAnimations.luxuryHover(node);
    const handleMouseEnter = () => hoverAnimation.play();
    const handleMouseLeave = () => hoverAnimation.reverse();

    node.addEventListener('mouseenter', handleMouseEnter);
    node.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      node.removeEventListener('mouseenter', handleMouseEnter);
      node.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  const typeColors = {
    educational: 'from-blue-500 to-indigo-600',
    industrial: 'from-gray-500 to-slate-600',
    recreational: 'from-green-500 to-emerald-600',
    commercial: 'from-luxury-gold to-luxury-gold-light'
  };

  return (
    <Card ref={cardRef} className={cn(
      "luxury-glass backdrop-blur-sm p-6 hover:shadow-2xl transition-all duration-500",
      "border border-luxury-gold/20 hover:border-luxury-gold/40"
    )}>
      <div className="flex items-start gap-4">
        <div className={cn(
          "w-12 h-12 rounded-xl bg-gradient-to-br flex items-center justify-center flex-shrink-0",
          typeColors[type]
        )}>
          {icon}
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="luxury-subheading text-lg font-semibold text-white mb-1">
            {name}
          </h3>
          {description && (
            <p className="luxury-body text-white/70 text-sm mb-2 line-clamp-2">
              {description}
            </p>
          )}
          
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1 text-luxury-gold">
              <MapPin className="w-3 h-3" />
              <span>{distance}</span>
            </div>
            <div className="flex items-center gap-1 text-white/60">
              <Clock className="w-3 h-3" />
              <span>{walkTime}</span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

const TransportCard: React.FC<{ 
  name: string; 
  distance: string; 
  duration: string; 
  icon: React.ReactNode; 
}> = ({ name, distance, duration, icon }) => {
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const node = cardRef.current;
    if (!node) return;

    const hoverAnimation = luxuryAnimations.luxuryHover(node);
    const handleMouseEnter = () => hoverAnimation.play();
    const handleMouseLeave = () => hoverAnimation.reverse();

    node.addEventListener('mouseenter', handleMouseEnter);
    node.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      node.removeEventListener('mouseenter', handleMouseEnter);
      node.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  return (
    <Card ref={cardRef} className={cn(
      "luxury-glass backdrop-blur-sm p-6 text-center hover:shadow-2xl transition-all duration-500",
      "border border-luxury-gold/20 hover:border-luxury-gold/40"
    )}>
      <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-luxury-gold to-luxury-gold-light rounded-2xl flex items-center justify-center">
        {icon}
      </div>
      
      <h3 className="luxury-subheading text-lg font-semibold text-white mb-2">
        {name}
      </h3>
      
      <div className="space-y-1 text-sm">
        <div className="flex items-center justify-center gap-2 text-luxury-gold">
          <MapPin className="w-3 h-3" />
          <span>{distance}</span>
        </div>
        <div className="flex items-center justify-center gap-2 text-white/60">
          <Clock className="w-3 h-3" />
          <span>{duration}</span>
        </div>
      </div>
    </Card>
  );
};

const LocationSection: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const landmarksRef = useRef<HTMLDivElement>(null);
  const transportRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    initGSAP();

    if (headerRef.current) {
      luxuryAnimations.fadeInUp(headerRef.current, 0.2);
    }

    if (landmarksRef.current) {
      luxuryAnimations.staggerFadeIn(landmarksRef.current.children, 0.1);
    }

    if (transportRef.current) {
      luxuryAnimations.staggerFadeIn(transportRef.current.children, 0.15);
    }

    if (mapRef.current) {
      luxuryAnimations.fadeInUp(mapRef.current, 0.8);
    }
  }, []);

  const landmarks: LandmarkProps[] = [
    {
      name: "NIT Rourkela",
      type: "educational",
      distance: "2.5 km",
      walkTime: "8 min drive",
      icon: <GraduationCap className="w-6 h-6 text-white" />,
      description: "Premier technical institute, one of India's leading NITs"
    },
    {
      name: "Rourkela Steel Plant",
      type: "industrial",
      distance: "4.2 km",
      walkTime: "12 min drive",
      icon: <Building className="w-6 h-6 text-white" />,
      description: "India's first integrated steel plant and major industrial landmark"
    },
    {
      name: "Hanuman Vatika",
      type: "recreational",
      distance: "1.8 km",
      walkTime: "6 min drive",
      icon: <TreePine className="w-6 h-6 text-white" />,
      description: "Beautiful temple complex and recreational area for spiritual wellness"
    },
    {
      name: "City Centre Mall",
      type: "commercial",
      distance: "3.1 km",
      walkTime: "10 min drive",
      icon: <Building className="w-6 h-6 text-white" />,
      description: "Modern shopping and entertainment complex with premium brands"
    }
  ];

  const transportOptions: TransportProps[] = [
    {
      type: "train",
      name: "Rourkela Railway Station",
      distance: "5.2 km",
      duration: "15 min drive",
      icon: <Train className="w-8 h-8 text-white" />
    },
    {
      type: "airport",
      name: "Biju Patnaik International Airport",
      distance: "340 km",
      duration: "4.5 hrs drive",
      icon: <Plane className="w-8 h-8 text-white" />
    },
    {
      type: "highway",
      name: "National Highway 143",
      distance: "2.8 km",
      duration: "8 min drive",
      icon: <Car className="w-8 h-8 text-white" />
    }
  ];

  return (
    <section 
      ref={sectionRef}
      id="location"
      className="relative py-20 lg:py-32 bg-luxury-charcoal overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-luxury-charcoal via-luxury-charcoal-light to-luxury-charcoal" />
      <div className="absolute top-0 left-0 w-full h-full opacity-5 bg-[url('https://images.pexels.com/photos/313782/pexels-photo-313782.jpeg?auto=compress&cs=tinysrgb&w=1920')] bg-cover bg-center" />
      
      {/* Decorative Elements */}
      <div className="absolute top-20 right-10 w-2 h-32 bg-gradient-to-b from-luxury-gold to-transparent opacity-60" />
      <div className="absolute bottom-20 left-10 w-2 h-32 bg-gradient-to-t from-luxury-gold to-transparent opacity-60" />

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div ref={headerRef} className="text-center mb-16">
          <div className="inline-flex items-center gap-2 luxury-glass rounded-full px-6 py-3 mb-6 backdrop-blur-sm">
            <Navigation className="w-4 h-4 text-luxury-gold" />
            <span className="luxury-caption text-white">Prime Location</span>
          </div>
          
          <h2 className="luxury-heading text-4xl md:text-6xl font-bold text-white mb-6">
            Located in the{' '}
            <span className="luxury-gradient-text">Heart</span>
            {' '}of Rourkela
          </h2>
          
          <p className="luxury-subheading text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Strategically positioned in Rourkela&apos;s most prestigious neighborhood, 
            surrounded by landmarks, educational institutions, and modern amenities
          </p>
        </div>

        {/* Landmarks Grid */}
        <div className="mb-20">
          <div className="flex items-center gap-3 mb-10">
            <Star className="w-6 h-6 text-luxury-gold" />
            <h3 className="luxury-subheading text-2xl font-semibold text-white">
              Nearby Landmarks
            </h3>
          </div>
          
          <div 
            ref={landmarksRef}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {landmarks.map((landmark, index) => (
              <LandmarkCard key={index} {...landmark} />
            ))}
          </div>
        </div>

        {/* Transportation */}
        <div className="mb-20">
          <div className="flex items-center gap-3 mb-10">
            <Navigation className="w-6 h-6 text-luxury-gold" />
            <h3 className="luxury-subheading text-2xl font-semibold text-white">
              Connectivity & Transport
            </h3>
          </div>
          
          <div 
            ref={transportRef}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {transportOptions.map((transport, index) => (
              <TransportCard key={index} {...transport} />
            ))}
          </div>
        </div>

        {/* Interactive Map Placeholder */}
        <div ref={mapRef}>
          <Card className="luxury-glass backdrop-blur-sm border border-luxury-gold/20 overflow-hidden">
            <div className="aspect-video bg-gradient-to-br from-luxury-charcoal-light to-luxury-charcoal relative">
              {/* Map placeholder with Pexels image */}
              <div 
                className="absolute inset-0 bg-cover bg-center opacity-60"
                style={{ 
                  backgroundImage: 'url(https://images.pexels.com/photos/313782/pexels-photo-313782.jpeg?auto=compress&cs=tinysrgb&w=800)' 
                }}
              />
              
              {/* Overlay content */}
              <div className="absolute inset-0 bg-luxury-charcoal/50 flex items-center justify-center">
                <div className="text-center">
                  <MapPin className="w-16 h-16 text-luxury-gold mx-auto mb-4" />
                  <h4 className="luxury-subheading text-2xl font-semibold text-white mb-2">
                    The Sixteen
                  </h4>
                  <p className="luxury-body text-white/80 mb-6">
                    Sector 6, Rourkela, Odisha
                  </p>
                  <Button variant="luxury" size="lg">
                    <Navigation className="w-4 h-4" />
                    Get Directions
                  </Button>
                </div>
              </div>
              
              {/* Location marker */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-4 h-4 bg-luxury-gold rounded-full animate-pulse shadow-lg shadow-luxury-gold/50" />
              </div>
            </div>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <Button variant="luxuryOutline" size="xl" className="luxury-shine-enhanced">
            <MapPin className="w-5 h-5" />
            Schedule Site Visit
          </Button>
        </div>
      </div>
    </section>
  );
};

export default LocationSection;